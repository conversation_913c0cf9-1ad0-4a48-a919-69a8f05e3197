using Newtonsoft.Json;
using SDCPCWeb.Models;
using SDCPCWeb.Models.BusinessFlow;
using SDCPCWeb.Models.Result;
using SDCPCWeb.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;
using SDCPCWeb.Models.System;
using SDCPCWeb.Models.CompanyRegister;
using Newtonsoft.Json.Linq;
using System.Data;
using SDCPCWeb.Models.BusinessContent.KanCeDingJie;
using SDCPCWeb.Models.BusinessContent;
using SDCPCWeb.Models.BusinessContent.YanXian;
using System.Text;
using System.Net;
using System.IO;
using System.IO.Compression;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Web.Configuration;
using Hangfire;
using SDCPCWeb.Jobs;
using SDCPCWeb.Models.EstateConstruction;
using System.Threading.Tasks;
using Hangfire.Console;
using Hangfire.Dashboard.Resources;
using Oracle.ManagedDataAccess.Client;
using SDCPCWeb.Extensions;
using SDCPCWeb.Models.Attachment;
using SDCPCWeb.Models.BusinessContent.GuiHuaDingDian;
using SDCPCWeb.Models.BusinessContent.GuiHuaHeShi;
using Hangfire.Server;
using System.ComponentModel;
using System.Text.RegularExpressions;
using SDCPCWeb.Encrypts;
using SDCPCWeb.Models.BusinessContent.ZhengChai;

namespace SDCPCWeb.Controllers {
    /// <summary>
    /// 业务流转管理接口
    /// </summary>
    [RoutePrefix("sdcapi/BusinessFlow"), BDCAuthorize]
    public class BusinessFlowManageController : ApiController {
        private readonly OracleDataService service = new OracleDataService();
        private string RootPath = ExternalApiConfig.RootPath;
        private UserInfo UserInfo => Request.Properties.ContainsKey("SDC-UserInfo") ? (UserInfo)Request.Properties["SDC-UserInfo"] : null;
        private static string xCloudApiUrl = ExternalApiConfig.xCloudApiUrl;
        private static string EPSOverallUploadMDBUrl = ExternalApiConfig.EPSOverallUploadMDBUrl;
        private static string MdbCheckApiUrl = ExternalApiConfig.MdbCheckApiUrl;

        #region 业主单位相关接口

        /// <summary>
        /// 业务创建
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost, Route("CreateNewBusiness")]
        public ApiResult CreateNewBusiness(BusinessBaseInfo request) {
            if (request == null) {
                return new ApiResult { StateCode = 0, Message = "创建失败，请输入正确的信息" };
            }
            //传入参数判断
            //必填值不能为空
            if (string.IsNullOrWhiteSpace(request.BusinessName)) {
                return new ApiResult { StateCode = 0, Message = "业务名称不能为空" };
            }
            if (string.IsNullOrWhiteSpace(request.BusinessClass)) {
                return new ApiResult { StateCode = 0, Message = "业务逻辑类不能为空" };
            }
            //根据业务类型获取流程类
            var flow = BusinessFlowConfig.GetFlow(Type.GetType($"SDCPCWeb.Models.BusinessFlow.{request.BusinessClass}"));
            if (flow == null) {
                return new ApiResult { StateCode = 0, Message = $"业务逻辑类{request.BusinessClass}尚未支持" };
            }

            if (request.BusinessClass == nameof(RealEstatePreCheckSurveyFlow) && DateTime.Now.Date >= new DateTime(2021, 6, 7)) {
                return new ApiResult { StateCode = 0, Message = $"从2021年6月7日零时起，不动产预核业务不能再新建业务，于2021年6月7日零时前已经提交的业务“我的业务”继续办理，如有疑问详询南宁市不动产登记中心测绘管理部0771-4306662" };
            }

            if (request.BusinessClass == nameof(RealEstatePreCheckSurveyAutoFlow)) {
                string pattern = @"^[\u4E00-\u9FA5-0-9]+$"; //包括了简体中文和繁体中文
                Regex regex = new Regex(pattern);
                bool result = regex.IsMatch(request.BusinessName) && (request.BusinessName.ToSimplifiedChinese() == request.BusinessName);
                if (result == false) {
                    return new ApiResult { StateCode = 0, Message = "业务名称只允许简体中文、阿拉伯数字和“-”符号！" };
                }
            }

            //判断楼盘表信息
            switch (request.BusinessClass) {
                case nameof(RealEstatePreSurveyBuildingTableChangeFlow):
                case nameof(RealEstatePreSurveyResultChangeFlow):
                case nameof(RealEstateActualBuildingTableChangeFlow):
                case nameof(RealEstateActualResultChangeFlow): {
                    if (string.IsNullOrWhiteSpace(request.ExtendInfo)) {
                        return new ApiResult { StateCode = 0, Message = $"楼盘表信息ExtendInfo不能为空" };
                    }
                    break;
                }
            }

            //已在立即处理前判断过人员权限，此处无需判断

            //获取第一个环节的执行者角色
            var firstActionRoles = flow.FlowActionInfo.Actions
                .FirstOrDefault(a => a.ID == flow.FlowActionInfo.StartActionId)?.ActionRoles;
            string Roles = "";
            firstActionRoles.ToList().ForEach(data => {
                if (Roles != "") {
                    Roles += ",";
                }
                Roles += data;
            });


            var developers = UserInfo.GetRelateDevelopCompanyList();
            //获取Cookie的值，取当前代表对应的建设单位
            var cid = Request.GetSDCCompanyIdFromCookie();
            var company = developers?.Keys.FirstOrDefault(c => c.ID == cid);
            if (company == null && developers?.Any() == true) {
                company = developers.Keys.FirstOrDefault();
            }

            //获取企业证件号和联系手机号
            string companyNo = company?.CreditCode;
            string companyContacterPhone = company?.ContacterPhone;
            if (!string.IsNullOrWhiteSpace(cid) && string.IsNullOrWhiteSpace(companyNo)) {
                var companyBaseInfo = service.GetById<CompanyBaseInfo>(cid);
                companyNo = companyBaseInfo?.CreditCode;
                companyContacterPhone = companyBaseInfo?.ContacterPhone;
            }

            //赋初始值
            request.ID = Guid.NewGuid().ToString("N");
            request.BusinessNumber = service.GetNewBusinessNo();
            request.CreateUserId = UserInfo.UserId;
            request.CreatePersonName = UserInfo.PersonName;
            request.CreatePersonNo = UserInfo.PersonNo;
            request.CreatePersonPhone = UserInfo.Phone.StartsWith("qy_") ? companyContacterPhone : UserInfo.Phone;
            request.CreateTime = DateTime.Now;
            request.StateCode = 1;
            request.BusinessType = flow.FlowName;
            request.DeveloperName = company?.CompanyName;
            request.DeveloperNo = company?.CreditCode;
            request.CreateCompanyNo = companyNo;
            if (request.BusinessClass == "LandSurveyProjectFlow")
            {
                request.SurveyCompanyNo = companyNo;
            }
            try {
                service.InsertOne(request);
                //创建业务的时候，创建第一个环节
                service.InsertOne(new BusinessLinkInfo {
                    ID = Guid.NewGuid().ToString("N"),
                    BusinessId = request.ID,
                    ActionId = flow.FlowActionInfo.StartActionId,
                    LinkName = flow.FlowActionInfo.Actions.FirstOrDefault(a => a.ID == flow.FlowActionInfo.StartActionId)?.Name,
                    CurrentUserID = UserInfo.UserId,
                    StartTime = DateTime.Now,
                    SignatureTime = DateTime.Now,
                    StateCode = 1,//第一个环节直接是签收状态
                    ActionUserRole = Roles
                });
                if (request.BusinessClass == "BaseSurveyDataDownloadFlow") {
                    service.InsertOne(new BaseGISData_ContentInfo {
                        ID = request.ID,
                        ProjectName = null,
                        ProjectBasis = null,
                        ProjectScope = null,
                        DataCheckState = 0,
                        DownLoadState = 0
                    });
                }
                if (request.BusinessClass == "MeasurePutLineFlow") {
                    service.InsertOne(new PutLine_ContentInfo {
                        ID = request.ID,
                        GroundCode = null,
                        ProjectPlanPermission = null,
                        DataCheckState = 0
                    });
                }
                if (request.BusinessClass == "RealEstatePreSurveyFlow") {
                    service.InsertOne(new PreSurvey_ContentInfo {
                        ID = request.ID,
                        GroundCode = null,
                        ProjectPlanPermission = null,
                        DataCheckState = 0
                    });
                }
                if (request.BusinessClass == "RealEstatePreCheckSurveyFlow" 
                    || request.BusinessClass == "RealEstatePreCheckSurveyAutoFlow") {
                    service.InsertOne(new PreSurvey_ContentInfo {
                        ID = request.ID,
                        GroundCode = null,
                        ProjectPlanPermission = null,
                        DataCheckState = 0
                    });
                    service.InsertOne(new PutLine_ContentInfo {
                        ID = request.ID,
                        GroundCode = null,
                        ProjectPlanPermission = null,
                        DataCheckState = 0
                    });
                }
                if (request.BusinessClass == "BlueLineSurveyFlow") {
                    service.InsertOne(new BlueLineSurveyContentInfo {
                        ID = request.ID,
                        DataCheckState = 0,
                        AchievementCheckState = 0,
                        DownLoadState = 0
                    });
                }
                if (request.BusinessClass == "MarkPointSurveyFlow") {
                    service.InsertOne(new MarkPointSurveyContentInfo {
                        ID = request.ID,
                        DataCheckState = 0,
                        AchievementCheckState = 0,
                        DownLoadState = 0
                    });
                }
                if (request.BusinessClass == "PlanCheckSurveyFlow") {
                    service.InsertOne(new PlanCheckSurveyContentInfo {
                        ID = request.ID,
                        GroundCode = null,
                        ProjectPlanPermission = null,
                        DataCheckState = 0
                    });
                }
                if (request.BusinessClass == "RealEstateActualSurveyFlow") {
                    service.InsertOne(new EstateActualSurveyContentInfo {
                        ID = request.ID,
                        GroundCode = null,
                        ProjectPlanPermission = null,
                        DataCheckState = 0,
                        CompanyAddress = company?.CompanyAddress,
                        LegalPersonName = company?.LegalPersonName,
                        LegalPersonNumber = company?.LegalPersonNumber,
                        IsZJ = "否",
                        PersonType = "法人"
                    });
                }
                if (request.BusinessClass == "RealEstatePreSurveyBuildingTableChangeFlow") {
                    service.InsertOne(new PreSurvey_ContentInfo {
                        ID = request.ID,
                        GroundCode = null,
                        ProjectPlanPermission = null,
                        DataCheckState = 0
                    });
                }
                if (request.BusinessClass == "RealEstatePreSurveyResultChangeFlow") {
                    service.InsertOne(new PreSurvey_ContentInfo {
                        ID = request.ID,
                        GroundCode = null,
                        ProjectPlanPermission = null,
                        DataCheckState = 0
                    });
                }
                if (request.BusinessClass == "RealEstateActualBuildingTableChangeFlow") {
                    service.InsertOne(new EstateActualSurveyContentInfo {
                        ID = request.ID,
                        GroundCode = null,
                        ProjectPlanPermission = null,
                        DataCheckState = 0
                    });
                }
                if (request.BusinessClass == "RealEstateActualResultChangeFlow") {
                    service.InsertOne(new EstateActualSurveyContentInfo {
                        ID = request.ID,
                        GroundCode = null,
                        ProjectPlanPermission = null,
                        DataCheckState = 0,
                        CompanyAddress = company?.CompanyAddress,
                        LegalPersonName = company?.LegalPersonName,
                        LegalPersonNumber = company?.LegalPersonNumber,
                        IsZJ = "否",
                        PersonType = "法人"
                    });
                }
                if (request.BusinessClass == "MeasurePutLinePreCheckFlow") {
                    service.InsertOne(new PutLine_ContentInfo {
                        ID = request.ID,
                        GroundCode = null,
                        ProjectPlanPermission = null,
                        DataCheckState = 0
                    });
                }
                if (request.BusinessClass == "CouncilPlanCheckFlow") {
                    service.InsertOne(new CouncilPlanCheckContentInfo {
                        ID = request.ID,
                        BusinessType = null,
                        BusinessTypeName = null,
                        ProjectPlanPermission = null,
                        DataCheckState = 0,
                        CompanyAddress = company?.CompanyAddress,
                        LegalPersonName = company?.LegalPersonName,
                        LegalPersonNumber = company?.LegalPersonNumber,
                        IsZJ = "否",
                        PersonType = "法人"
                    });
                }
                if (request.BusinessClass == "CouncilMeasurePutLinePreCheckFlow") {
                    service.InsertOne(new PutLine_ContentInfo {
                        ID = request.ID,
                        GroundCode = null,
                        ProjectPlanPermission = null,
                        DataCheckState = 0
                    });
                }
                if (request.BusinessClass == "RealEstateOverallActualSurveyFlow") {
                    service.InsertOne(new EstateActualSurveyContentInfo {
                        ID = request.ID,
                        GroundCode = null,
                        ProjectPlanPermission = null,
                        DataCheckState = 0,
                        CompanyAddress = company?.CompanyAddress,
                        LegalPersonName = company?.LegalPersonName,
                        LegalPersonNumber = company?.LegalPersonNumber,
                        IsZJ = "否",
                        PersonType = "法人"
                    });
                }
                if (request.BusinessClass == "LandSurveyProjectFlow") {
                    service.InsertOne(new LandSurvey_ContentInfo {
                        ID = request.ID,
                        DataCheckState = 0,
                    });
                }

                //记录操作日志
                HttpRequestBase req = ((HttpContextBase)Request.Properties["MS_HttpContext"]).Request;
                var reqInfoHelper = new HttpRequestInfoHelper(req);
                LogService.WriteLogs(Request, "业务管理", $"创建业务 >> 业务ID：{request.ID} >> 业务类型：{request.BusinessType}", reqInfoHelper);

                return new ApiResult { StateCode = 1, Data = request.ID, Message = "创建成功" };
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "创建失败，错误信息：" + e.Message + "" };
            }
        }

        /// <summary>
        /// 业务保存
        /// </summary>
        /// <param name="id"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost, Route("SaveBusiness")]
        public async Task<ApiResult> SaveBusiness(string id, JObject model) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "ID不能为空，请传入正确的参数" };
            }
            if (model == null) {
                return new ApiResult { StateCode = 0, Message = "请输入正确的信息" };
            }
            //ID必须存在对应的记录
            var record = service.GetById<BusinessBaseInfo>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "保存失败，无法找到业务信息" };
            }
            try {
                string result = "";
                string obj = model["model"].ToString();
                if (record.BusinessClass == "BaseSurveyDataDownloadFlow") {
                    BaseSurveyDataDownloadProject project = JsonConvert.DeserializeObject<BaseSurveyDataDownloadProject>(obj);
                    result = BaseSurveyDataDownloadProject.Save(project);
                }
                if (record.BusinessClass == "MeasurePutLineFlow") {
                    MeasurePutLineProject project = JsonConvert.DeserializeObject<MeasurePutLineProject>(obj);
                    var rec = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(project.ContentInfo.ProjectPlanPermission);
                    var sel = rec.Select(r => CanProjectPlanPermissionUse(id, "MeasurePutLineFlow", r.Code));
                    if (sel.Any(s => s != "")) {
                        return new ApiResult { StateCode = 0, Message = string.Join(";", sel.Where(s => s != "").Distinct()) };
                    }
                    result = MeasurePutLineProject.Save(project);
                }
                if (record.BusinessClass == "RealEstatePreSurveyFlow") {
                    RealEstatePreSurveyProject project = JsonConvert.DeserializeObject<RealEstatePreSurveyProject>(obj);
                    var rec = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(project.ContentInfo.ProjectPlanPermission);
                    var sel = rec.Select(r => CanProjectPlanPermissionUse(id, "RealEstatePreSurveyFlow", r.Code));
                    if (sel.Any(s => s != "")) {
                        return new ApiResult { StateCode = 0, Message = string.Join(";", sel.Where(s => s != "").Distinct()) };
                    }
                    var selCheck = rec.Select(r => CanProjectPlanPermissionUse(id, "RealEstatePreCheckSurveyFlow", r.Code));
                    if (selCheck.Any(s => s != "")) {
                        return new ApiResult { StateCode = 0, Message = string.Join(";", selCheck.Where(s => s != "").Distinct()) };
                    }
                    result = RealEstatePreSurveyProject.Save(project);
                }
                if (record.BusinessClass == "RealEstatePreCheckSurveyFlow") {
                    RealEstatePreCheckSurveyProject project = JsonConvert.DeserializeObject<RealEstatePreCheckSurveyProject>(obj);
                    var rec = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(project.PreSurveyContentInfo.ProjectPlanPermission);
                    var sel = rec.Select(r => CanProjectPlanPermissionUse(id, "RealEstatePreSurveyFlow", r.Code));
                    if (sel.Any(s => s != "")) {
                        return new ApiResult { StateCode = 0, Message = string.Join(";", sel.Where(s => s != "").Distinct()) };
                    }
                    var selCheck = rec.Select(r => CanProjectPlanPermissionUse(id, "RealEstatePreCheckSurveyFlow", r.Code));
                    if (selCheck.Any(s => s != "")) {
                        return new ApiResult { StateCode = 0, Message = string.Join(";", selCheck.Where(s => s != "").Distinct()) };
                    }
                    result = RealEstatePreCheckSurveyProject.Save(project);
                }
                if (record.BusinessClass == "BlueLineSurveyFlow") {
                    BlueLineSurveyProject project = JsonConvert.DeserializeObject<BlueLineSurveyProject>(obj);
                    result = BlueLineSurveyProject.Save(project);
                }
                if (record.BusinessClass == "MarkPointSurveyFlow") {
                    MarkPointSurveyProject project = JsonConvert.DeserializeObject<MarkPointSurveyProject>(obj);
                    result = MarkPointSurveyProject.Save(project);
                }
                if (record.BusinessClass == "PlanCheckSurveyFlow") {
                    //PlanCheckSurveyProject project = JsonConvert.DeserializeObject<PlanCheckSurveyProject>(obj);
                    //result = PlanCheckSurveyProject.Save(project);
                    result = "暂不支持该业务";
                }
                if (record.BusinessClass == "RealEstateActualSurveyFlow") {
                    RealEstateActualSurveyProject project = JsonConvert.DeserializeObject<RealEstateActualSurveyProject>(obj);
                    result = await RealEstateActualSurveyProject.Save(project);
                }
                if (record.BusinessClass == "RealEstatePreSurveyBuildingTableChangeFlow") {
                    RealEstatePreSurveyBuildingTableChangeProject project = JsonConvert.DeserializeObject<RealEstatePreSurveyBuildingTableChangeProject>(obj);

                    var jArr = JArray.Parse(project.BaseInfo.ExtendInfo);
                    var zrzguids = jArr.Select(s => new Tuple<string, string>(s["ZRZGUID"].ToString(), s["ZL"].ToString())).ToList();
                    var checkResult = CanZRZUse(id, record.BusinessClass, zrzguids);
                    if (checkResult.Any()) {
                        return new ApiResult { StateCode = 0, Message = $"保存失败,失败原因：{string.Join("；", checkResult)}" };
                    }

                    result = RealEstatePreSurveyBuildingTableChangeProject.Save(project);
                }
                if (record.BusinessClass == "RealEstatePreSurveyResultChangeFlow") {
                    RealEstatePreSurveyResultChangeProject project = JsonConvert.DeserializeObject<RealEstatePreSurveyResultChangeProject>(obj);

                    var jArr = JArray.Parse(project.BaseInfo.ExtendInfo);
                    var zrzguids = jArr.Select(s => new Tuple<string, string>(s["ZRZGUID"].ToString(), s["ZL"].ToString())).ToList();
                    var checkResult = CanZRZUse(id, record.BusinessClass, zrzguids);
                    if (checkResult.Any()) {
                        return new ApiResult { StateCode = 0, Message = $"保存失败,失败原因：{string.Join("；", checkResult)}" };
                    }

                    result = RealEstatePreSurveyResultChangeProject.Save(project);
                }
                if (record.BusinessClass == "RealEstateActualBuildingTableChangeFlow") {
                    RealEstateActualBuildingTableChangeProject project = JsonConvert.DeserializeObject<RealEstateActualBuildingTableChangeProject>(obj);

                    var jArr = JArray.Parse(project.BaseInfo.ExtendInfo);
                    var zrzguids = jArr.Select(s => new Tuple<string, string>(s["ZRZGUID"].ToString(), s["ZL"].ToString())).ToList();
                    var checkResult = CanZRZUse(id, record.BusinessClass, zrzguids);
                    if (checkResult.Any()) {
                        return new ApiResult { StateCode = 0, Message = $"保存失败,失败原因：{string.Join("；", checkResult)}" };
                    }

                    result = RealEstateActualBuildingTableChangeProject.Save(project);
                }
                if (record.BusinessClass == "RealEstateActualResultChangeFlow") {
                    RealEstateActualResultChangeProject project = JsonConvert.DeserializeObject<RealEstateActualResultChangeProject>(obj);

                    var jArr = JArray.Parse(project.BaseInfo.ExtendInfo);
                    var zrzguids = jArr.Select(s => new Tuple<string, string>(s["ZRZGUID"].ToString(), s["ZL"].ToString())).ToList();
                    var checkResult = CanZRZUse(id, record.BusinessClass, zrzguids);
                    if (checkResult.Any()) {
                        return new ApiResult { StateCode = 0, Message = $"保存失败,失败原因：{string.Join("；", checkResult)}" };
                    }

                    result = RealEstateActualResultChangeProject.Save(project);
                }
                if (record.BusinessClass == "MeasurePutLinePreCheckFlow") {
                    MeasurePutLinePreCheckProject project = JsonConvert.DeserializeObject<MeasurePutLinePreCheckProject>(obj);
                    var rec = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(project.ContentInfo.ProjectPlanPermission);
                    var sel = rec.Select(r => CanProjectPlanPermissionUse(id, "MeasurePutLinePreCheckFlow", r.Code));
                    if (sel.Any(s => s != "")) {
                        return new ApiResult { StateCode = 0, Message = string.Join(";", sel.Where(s => s != "").Distinct()) };
                    }
                    result = MeasurePutLinePreCheckProject.Save(project);
                }
                if (record.BusinessClass == nameof(RealEstatePreCheckSurveyAutoFlow)) {
                    RealEstatePreCheckSurveyAutoProject project = JsonConvert.DeserializeObject<RealEstatePreCheckSurveyAutoProject>(obj);

                    /*
                    //判断业务名称是否可用
                    string pattern = @"^[\u4E00-\u9FA5-0-9]+$"; //包括了简体中文和繁体中文
                    Regex regex = new Regex(pattern);
                    bool checkBusinessName = regex.IsMatch(project.BaseInfo.BusinessName) && (project.BaseInfo.BusinessName.ToSimplifiedChinese() == project.BaseInfo.BusinessName);
                    if (checkBusinessName == false) {
                        return new ApiResult { StateCode = 0, Message = "业务名称只允许简体中文、阿拉伯数字和“-”符号！" };
                    }
                    */

                    //检查宗地号是否可用
                    var canUseGroundCodeResult = await CanGroundCodeUse(project.PreSurveyContentInfo.GroundCode, record.DeveloperName);
                    if (!string.IsNullOrEmpty(canUseGroundCodeResult)) {
                        return new ApiResult {StateCode = 0, Message = canUseGroundCodeResult };
                    }

                    var rec = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(project.PreSurveyContentInfo.ProjectPlanPermission);

                    //判断是否自行添加的工规证号
                    var newRec = rec.Where(s => s.IsAddNew == true).ToList();
                    if (newRec.Any()) {
                        foreach (var item in newRec) {
                            if (!item.Code.StartsWith("450021") || item.Code.Length != 15) {
                                return new ApiResult { StateCode = 0, Message = "保存失败,失败原因：工程规划许可证号[" + item.Code + "]格式不正确" };
                            }
                        }
                    }

                    var sel = rec.Select(r => CanProjectPlanPermissionUse(id, "RealEstatePreSurveyFlow", r.Code));
                    if (sel.Any(s => s != "")) {
                        return new ApiResult { StateCode = 0, Message = string.Join(";", sel.Where(s => s != "").Distinct()) };
                    }
                    var selCheck = rec.Select(r => CanProjectPlanPermissionUse(id, "RealEstatePreCheckSurveyAutoFlow", r.Code));
                    if (selCheck.Any(s => s != "")) {
                        return new ApiResult { StateCode = 0, Message = string.Join(";", selCheck.Where(s => s != "").Distinct()) };
                    }
                    result = RealEstatePreCheckSurveyAutoProject.Save(project);
                }
                if (record.BusinessClass == nameof(CouncilPlanCheckFlow)) {
                    CouncilPlanCheckProject project = JsonConvert.DeserializeObject<CouncilPlanCheckProject>(obj);
                    var rec = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(project.ContentInfo.ProjectPlanPermission);
                    var sel = rec.Select(r => CanProjectPlanPermissionUse(id, nameof(CouncilPlanCheckFlow), r.Code));
                    if (sel.Any(s => s != "")) {
                        return new ApiResult { StateCode = 0, Message = string.Join(";", sel.Where(s => s != "").Distinct()) };
                    }

                    if (string.IsNullOrWhiteSpace(project.ContentInfo.BusinessType)) {
                        return new ApiResult { StateCode = 0, Message = "保存失败,失败原因：业务类型不能为空" };
                    }

                    switch (project.ContentInfo.BusinessType) {
                        case "1": {
                            project.ContentInfo.BusinessTypeName = "道路桥梁类";
                            break;
                        }
                        case "2": {
                            project.ContentInfo.BusinessTypeName = "地下（上）管线类";
                            break;
                        }
                        case "3": {
                            project.ContentInfo.BusinessTypeName = "市政场站类";
                            break;
                        }
                        case "4": {
                            project.ContentInfo.BusinessTypeName = "水系工程类";
                            break;
                        }
                        default: {
                            return new ApiResult { StateCode = 0, Message = $"保存失败,失败原因：不支持的业务类型[{project.ContentInfo.BusinessType}]" };
                        }
                    }

                    result = CouncilPlanCheckProject.Save(project);
                }
                if (record.BusinessClass == nameof(CouncilMeasurePutLinePreCheckFlow)) {
                    CouncilMeasurePutLinePreCheckProject project = JsonConvert.DeserializeObject<CouncilMeasurePutLinePreCheckProject>(obj);
                    var rec = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(project.ContentInfo.ProjectPlanPermission);
                    var sel = rec.Select(r => CanProjectPlanPermissionUse(id, "CouncilMeasurePutLinePreCheckFlow", r.Code));
                    if (sel.Any(s => s != "")) {
                        return new ApiResult { StateCode = 0, Message = string.Join(";", sel.Where(s => s != "").Distinct()) };
                    }
                    result = CouncilMeasurePutLinePreCheckProject.Save(project);
                }
                if (record.BusinessClass == nameof(RealEstateOverallActualSurveyFlow)) {
                    RealEstateOverallActualSurveyProject project = JsonConvert.DeserializeObject<RealEstateOverallActualSurveyProject>(obj);
                    result = RealEstateOverallActualSurveyProject.Save(project);
                }
                if (result == "") {
                    //记录操作日志
                    HttpRequestBase req = ((HttpContextBase)Request.Properties["MS_HttpContext"]).Request;
                    var reqInfoHelper = new HttpRequestInfoHelper(req);
                    LogService.WriteLogs(Request, "业务管理", $"编辑业务 >> 业务ID：{record.ID} >> 业务类型：{record.BusinessType}", reqInfoHelper);
                    return new ApiResult { StateCode = 1, Message = "保存成功" };
                }
                else {
                    return new ApiResult { StateCode = 0, Message = "保存失败,失败原因：" + result + "" };
                }
            }
            catch (Exception e) {
                //记录操作日志
                HttpRequestBase req = ((HttpContextBase)Request.Properties["MS_HttpContext"]).Request;
                var reqInfoHelper = new HttpRequestInfoHelper(req);
                LogService.WriteLogs(Request, "业务管理异常", $"编辑业务 >> 业务ID：{record.ID} >> 业务类型：{record.BusinessType} >> 异常消息：{e.GetStackTraces()}", reqInfoHelper);
                return new ApiResult { StateCode = 0, Message = "保存失败，错误信息：" + e.Message + "" };
            }
        }

        /// <summary>
        /// 获取工程规划许可证信息
        /// </summary>
        /// <param name="businessId"></param>
        /// <param name="businessClass"></param>
        /// <param name="code">许可证号或者项目编号</param>
        /// <returns></returns>
        [HttpGet, Route("GetProjectPlanPermissionInfo")]
        public ApiResult GetProjectPlanPermissionInfo(string businessId, string businessClass, string code) {
            if (string.IsNullOrEmpty(code)) {
                return new ApiResult { StateCode = 0, Message = "请输入正确的工程规划许可证号" };
            }
            string rel = CanProjectPlanPermissionUse(businessId, businessClass, code);
            if (rel != "") {
                return new ApiResult { StateCode = 0, Message = rel };
            }

            string url = ExternalApiConfig.ServiceUrl1;
            url += $"?currentPage=1&currentCount=20&text={code}";

            //市政工程建设竣工规划核实流程
            if (businessClass == nameof(CouncilPlanCheckFlow) || businessClass == nameof(CouncilMeasurePutLinePreCheckFlow)) {
                url = ExternalApiConfig.SZServiceUrl;
                url += $"?text={code}";
            }

            try {
                string result = Get(url);

                //市政工程建设竣工规划核实流程
                if (businessClass == nameof(CouncilPlanCheckFlow) || businessClass == nameof(CouncilMeasurePutLinePreCheckFlow)) {
                    if (result.Trim() == "[]") {
                        return new ApiResult { StateCode = 2, Message = "该《建设工程规划许可证》证号未找到，请核对" };
                    }
                }

                if (result.Trim() == "[]") {
                    return new ApiResult {StateCode = 2, Message = "该《建设工程规划许可证》证号未找到，请核对"};
                }

                /*
                if (result.Trim() == "[]") {

                    string url1 = ExternalApiConfig.ServiceUrl1;
                    url1 += $"?text={code}";
                    result = Get(url1);
                    if (result.Trim() == "[]") {
                        return new ApiResult {StateCode = 2, Message = "该《建设工程规划许可证》证号未找到，请核对"};
                    }
                }
                */

                JArray ja = (JArray)JsonConvert.DeserializeObject(result);
                //待处理
                ProjectPlanPermissionInfo project = new ProjectPlanPermissionInfo();
                project.Code = ja[0]["certificateNumber"].ToString();
                if (string.IsNullOrWhiteSpace(project.Code)) {
                    return new ApiResult { StateCode = 2, Message = "该《建设工程规划许可证》证号未找到，请核对" };
                }

                //如果不是市、经、高开头的报建编号，就不允许办理业务
                if (businessClass == nameof(CouncilPlanCheckFlow) ||
                    businessClass == nameof(CouncilMeasurePutLinePreCheckFlow)) {
                    project.CaseCode = ja[0]["casecode"].ToString();
                    project.SPNR = ja[0]["spnr"].ToString();

                    if (project.CaseCode.StartsWith("市") || project.CaseCode.StartsWith("经") ||
                        project.CaseCode.StartsWith("高")) {
                    }
                    else
                    {
                        string flowName = string.Empty;
                        if (businessClass == nameof(CouncilPlanCheckFlow))
                            flowName = BusinessFlowConfig.GetFlow<CouncilPlanCheckFlow>().FlowName;
                        if (businessClass == nameof(CouncilMeasurePutLinePreCheckFlow))
                            flowName = BusinessFlowConfig.GetFlow<CouncilMeasurePutLinePreCheckFlow>().FlowName;
                        return new ApiResult { StateCode = 3, Message = $"{flowName}线上办理仅对市局、经开分局、高新分局审批的项目开放；五象、各城区局审批的项目请到对应部门办理" };
                    }
                }
                else {
                    project.CaseCode = ja[0]["casecode"]?.ToString();
                }
                

                if (businessClass == nameof(RealEstateOverallActualSurveyFlow)) {
                    if (project.Code.Length > 10 && project.Code.Substring(10, 1) == "5") {
                        return new ApiResult { StateCode = 3, Message = $"经检查该项目位于五象新区，暂不支持线上办理全面核实，请到五象分局对应部门办理" };
                    }
                }

                project.ConstructCompany = ja[0]["buildunit"].ToString();
                project.Address = ja[0]["buildaddress"].ToString();
                project.ProjectName = ja[0]["projectname"].ToString();
                project.AppendixImgNumber = ja[0]["projectcode"].ToString();
                JObject gc = (JObject)JsonConvert.DeserializeObject(ja[0]["gc"].ToString());
                JArray lb = (JArray)JsonConvert.DeserializeObject(ja[0]["lb"].ToString());
                project.Buildings = new List<Building>();
                foreach (JObject l in lb) {
                    Building item = new Building {
                        Type = l["fd01"].ToString(),
                        Area = string.IsNullOrEmpty(l["fd02"].ToString()) ? 0 : Convert.ToDecimal(l["fd02"])
                    };
                    project.Buildings.Add(item);
                }
                project.BuildingNature = gc["fd01"].ToString();
                project.BuildingStructure = gc["fd02"].ToString();
                project.TotalArea = new TotalAreaOrFloor {
                    Aboveground = string.IsNullOrEmpty(gc["fd17"].ToString()) ? 0 : Convert.ToDecimal(gc["fd17"]),
                    Underground = string.IsNullOrEmpty(gc["fd18"].ToString()) ? 0 : Convert.ToDecimal(gc["fd18"])
                };
                project.BaseArea = string.IsNullOrEmpty(gc["fd16"].ToString()) ? 0 : Convert.ToDecimal(gc["fd16"]);
                //fd12 地上层数，同一栋不同单元有不同层高，取最大值为层高值 from 李俊双 2020-11-20
                var aboveground = gc["fd12"].ToString();
                var underground = gc["fd13"].ToString();

                if (aboveground.Contains("/")) {
                    var multiFloors = aboveground.Split("/".ToCharArray(), StringSplitOptions.RemoveEmptyEntries);
                    decimal tmpResult;
                    if (multiFloors.All(f => decimal.TryParse(f, out tmpResult))) {
                        //全部都是数值型，取最大值
                        aboveground = multiFloors.Select(Convert.ToDecimal).Max().ToString();
                    }
                }
                else if (aboveground.Contains("F+D")) {
                    //地上层数如果是这么写的话，那就直接从这个字符串中提取地上层数和地下层数
                    var multiParts = aboveground.Replace("F+D", ",")
                        .Split(",".ToCharArray(), StringSplitOptions.RemoveEmptyEntries);
                    aboveground = multiParts[0];
                    underground = multiParts[1];
                }
                //fd13 地下层数，同一栋不同单元有不同层高，取最大值为层高值 from 李俊双 2020-12-30
                if (underground.Contains("/")) {
                    var multiFloors = underground.Split("/".ToCharArray(), StringSplitOptions.RemoveEmptyEntries);
                    decimal tmpResult;
                    if (multiFloors.All(f => decimal.TryParse(f, out tmpResult))) {
                        //全部都是数值型，取最大值
                        underground = multiFloors.Select(Convert.ToDecimal).Max().ToString();
                    }
                }
                project.Floors = new TotalAreaOrFloor {
                    Aboveground = string.IsNullOrEmpty(aboveground) ? 0 : Convert.ToDecimal(aboveground),
                    Underground = string.IsNullOrEmpty(underground) ? 0 : Convert.ToDecimal(underground)
                };
                project.BuildingHeight = string.IsNullOrEmpty(gc["fd07"].ToString()) ? 0 : Convert.ToDecimal(gc["fd07"]);
                project.BuildingBlockNumber = string.IsNullOrEmpty(gc["fd06"].ToString()) ? 0 : Convert.ToInt32(gc["fd06"]);
                project.Invest = string.IsNullOrEmpty(gc["fd08"].ToString()) ? 0 : Convert.ToDecimal(gc["fd08"]);
                JArray tx = (JArray)JsonConvert.DeserializeObject(ja[0]["tx"].ToString());
                project.SetType = new List<SetType>();
                foreach (JObject t in tx) {
                    SetType item = new SetType {
                        Name = t["fd01"].ToString(),
                        Number = string.IsNullOrEmpty(t["fd02"].ToString()) ? 0 : Convert.ToInt32(t["fd02"]),
                        Area = string.IsNullOrEmpty(t["fd03"].ToString()) ? 0 : Convert.ToDecimal(t["fd03"])
                    };
                    project.SetType.Add(item);
                }
                project.Others = new SetType();

                project.BalanceCJPTF = ja[0]["balanceCJPTF"]?.ToString();
                project.CertificateDate = ja[0]["certificateDate"]?.ToString();

                return new ApiResult { StateCode = 1, Data = project };
            }
            catch (Exception e) {
                Request.WriteSDCLog("获取工规证号信息", $"工规证号：{code} >> 错误信息：{e.GetStackTraces()} ");
                return new ApiResult { StateCode = 0, Message = "获取失败，错误信息：" + e.Message + "" };
            }
        }

        /// <summary>
        /// 获取已排序的测绘单位列表
        /// </summary>
        /// <param name="businessClass"></param>
        /// <param name="companyname"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpGet, Route("GetSurveyCompanyList")]
        public ApiResult GetOrderedSurveyCompanyList(string businessClass, string companyname, int pageIndex = 1, int pageSize = 10) {
            if (string.IsNullOrWhiteSpace(businessClass)) {
                return new ApiResult { StateCode = 0, Message = "参数businessClass不能为空" };
            }
            //获取流程信息
            var type = Type.GetType($"SDCPCWeb.Models.BusinessFlow.{businessClass}");
            if (type == null) {
                return new ApiResult { StateCode = 0, Message = $"业务类型代码{businessClass}不支持" };
            }
            string where = "WHERE B.BUSINESSCLASS=:classname\n";
            var parameters = new List<OracleParameter>();
            parameters.Add(new OracleParameter(":classname", OracleDbType.Varchar2) { Value = businessClass });

            if (!string.IsNullOrWhiteSpace(companyname)) {
                where += "AND C.COMPANYNAME LIKE :companyname";
                parameters.Add(new OracleParameter(":companyname", OracleDbType.Varchar2) { Value = $"%{companyname}%" });
            }

            var pageList = service.GetPageResultWithSql<SurveyCompanyListItem>(SurveyCompanyListItem.MainSql + where,
                "ListOrder", pageIndex, pageSize, parameters.ToArray());

            return new ApiResult() { StateCode = 1, Data = pageList };

        }

        /// <summary>
        /// 委托测绘单位并提交业务
        /// </summary>
        /// <param name="businessId"></param>
        /// <param name="actionId"></param>
        /// <param name="creditCode"></param>
        /// <returns></returns>
        [HttpPost, Route("EntrustSurveyCompany")]
        public ApiResult EntrustSurveyCompany(string businessId, string actionId, string creditCode) {
            if (string.IsNullOrEmpty(businessId)) {
                return new ApiResult { StateCode = 0, Message = "业务ID不能为空" };
            }
            if (string.IsNullOrEmpty(actionId)) {
                return new ApiResult { StateCode = 0, Message = "当前环节ID不能为空" };
            }
            if (string.IsNullOrEmpty(creditCode)) {
                return new ApiResult { StateCode = 0, Message = "测绘单位统一社会信用代码不能为空" };
            }
            var userId = UserInfo.UserId;
            try {
                var record = service.GetById<BusinessBaseInfo>(businessId);
                var companyInfo = service.GetList<CompanyBaseInfo>("CreditCode='" + creditCode + "'");
                if (companyInfo.Count == 0) {
                    return new ApiResult { StateCode = 0, Message = "测绘单位信息无效" };
                }
                string cannotFlowPostMessage;
                if (!CanFlowPost(record, actionId, userId, out cannotFlowPostMessage)) {
                    return new ApiResult { StateCode = 0, Message = $"操作失败，{cannotFlowPostMessage}" };
                }
                record.SurveyCompanyName = companyInfo[0].CompanyName;
                record.SurveyCompanyNo = creditCode;
                service.UpdateOne(record);
                Request.WriteSDCLog("业务办理", $"委托测绘单位 >> 业务ID：{record.ID} >> 申请单位：{record.DeveloperName} >> 测绘单位：{record.SurveyCompanyName} ");
                if (record.BusinessClass == "BaseSurveyDataDownloadFlow") {
                    FlowService<BaseSurveyDataDownloadFlow> flow = new FlowService<BaseSurveyDataDownloadFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    BaseSurveyDataDownloadProject project = BaseSurveyDataDownloadProject.GetByBusinessID(businessId);
                    return new ApiResult { StateCode = 1, Data = project, Message = "委托成功" };
                }
                if (record.BusinessClass == "MeasurePutLineFlow") {
                    FlowService<MeasurePutLineFlow> flow = new FlowService<MeasurePutLineFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    MeasurePutLineProject project = MeasurePutLineProject.GetByBusinessID(businessId);
                    return new ApiResult { StateCode = 1, Data = project, Message = "委托成功" };
                }
                if (record.BusinessClass == "RealEstatePreSurveyFlow") {
                    FlowService<RealEstatePreSurveyFlow> flow = new FlowService<RealEstatePreSurveyFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    RealEstatePreSurveyProject project = RealEstatePreSurveyProject.GetByBusinessID(businessId);
                    return new ApiResult { StateCode = 1, Data = project, Message = "委托成功" };
                }
                if (record.BusinessClass == "RealEstatePreCheckSurveyFlow") {
                    FlowService<RealEstatePreCheckSurveyFlow> flow = new FlowService<RealEstatePreCheckSurveyFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    RealEstatePreCheckSurveyProject project = RealEstatePreCheckSurveyProject.GetByBusinessID(businessId);
                    return new ApiResult { StateCode = 1, Data = project, Message = "委托成功" };
                }
                if (record.BusinessClass == "BlueLineSurveyFlow") {
                    FlowService<BlueLineSurveyFlow> flow = new FlowService<BlueLineSurveyFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    BlueLineSurveyProject project = BlueLineSurveyProject.GetByBusinessID(businessId);
                    return new ApiResult { StateCode = 1, Data = project, Message = "委托成功" };
                }
                if (record.BusinessClass == "MarkPointSurveyFlow") {
                    FlowService<MarkPointSurveyFlow> flow = new FlowService<MarkPointSurveyFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    MarkPointSurveyProject project = MarkPointSurveyProject.GetByBusinessID(businessId);
                    return new ApiResult { StateCode = 1, Data = project, Message = "委托成功" };
                }
                if (record.BusinessClass == "PlanCheckSurveyFlow") {
                    //FlowService<PlanCheckSurveyFlow> flow = new FlowService<PlanCheckSurveyFlow>(service);
                    //flow.FlowPostToNext(record.ID);
                    //PlanCheckSurveyProject project = PlanCheckSurveyProject.GetByBusinessID(businessId);
                    //return new ApiResult { StateCode = 1, Data = project, Message = "委托成功" };
                    return new ApiResult { StateCode = 0, Message = "委托失败，暂不支持该业务" };
                }
                if (record.BusinessClass == "RealEstateActualSurveyFlow") {
                    FlowService<RealEstateActualSurveyFlow> flow = new FlowService<RealEstateActualSurveyFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    RealEstateActualSurveyProject project = RealEstateActualSurveyProject.GetByBusinessID(businessId);
                    return new ApiResult { StateCode = 1, Data = project, Message = "委托成功" };
                }
                if (record.BusinessClass == "RealEstatePreSurveyBuildingTableChangeFlow") {
                    FlowService<RealEstatePreSurveyBuildingTableChangeFlow> flow = new FlowService<RealEstatePreSurveyBuildingTableChangeFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    RealEstatePreSurveyBuildingTableChangeProject project = RealEstatePreSurveyBuildingTableChangeProject.GetByBusinessID(businessId);
                    return new ApiResult { StateCode = 1, Data = project, Message = "委托成功" };
                }
                if (record.BusinessClass == "RealEstatePreSurveyResultChangeFlow") {
                    FlowService<RealEstatePreSurveyResultChangeFlow> flow = new FlowService<RealEstatePreSurveyResultChangeFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    RealEstatePreSurveyResultChangeProject project = RealEstatePreSurveyResultChangeProject.GetByBusinessID(businessId);
                    return new ApiResult { StateCode = 1, Data = project, Message = "委托成功" };
                }
                if (record.BusinessClass == "RealEstateActualBuildingTableChangeFlow") {
                    FlowService<RealEstateActualBuildingTableChangeFlow> flow = new FlowService<RealEstateActualBuildingTableChangeFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    RealEstateActualBuildingTableChangeProject project = RealEstateActualBuildingTableChangeProject.GetByBusinessID(businessId);
                    return new ApiResult { StateCode = 1, Data = project, Message = "委托成功" };
                }
                if (record.BusinessClass == "RealEstateActualResultChangeFlow") {
                    FlowService<RealEstateActualResultChangeFlow> flow = new FlowService<RealEstateActualResultChangeFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    RealEstateActualResultChangeProject project = RealEstateActualResultChangeProject.GetByBusinessID(businessId);
                    return new ApiResult { StateCode = 1, Data = project, Message = "委托成功" };
                }
                if (record.BusinessClass == "MeasurePutLinePreCheckFlow") {
                    FlowService<MeasurePutLinePreCheckFlow> flow = new FlowService<MeasurePutLinePreCheckFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    MeasurePutLinePreCheckProject project = MeasurePutLinePreCheckProject.GetByBusinessID(businessId);
                    return new ApiResult { StateCode = 1, Data = project, Message = "委托成功" };
                }
                if (record.BusinessClass == "RealEstatePreCheckSurveyAutoFlow") {
                    FlowService<RealEstatePreCheckSurveyAutoFlow> flow = new FlowService<RealEstatePreCheckSurveyAutoFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    RealEstatePreCheckSurveyAutoProject project = RealEstatePreCheckSurveyAutoProject.GetByBusinessID(businessId);
                    return new ApiResult { StateCode = 1, Data = project, Message = "委托成功" };
                }
                if (record.BusinessClass == "CouncilPlanCheckFlow") {
                    FlowService<CouncilPlanCheckFlow> flow = new FlowService<CouncilPlanCheckFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    CouncilPlanCheckProject project = CouncilPlanCheckProject.GetByBusinessID(businessId);
                    return new ApiResult { StateCode = 1, Data = project, Message = "委托成功" };
                }
                if (record.BusinessClass == "CouncilMeasurePutLinePreCheckFlow") {
                    FlowService<CouncilMeasurePutLinePreCheckFlow> flow = new FlowService<CouncilMeasurePutLinePreCheckFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    CouncilMeasurePutLinePreCheckProject project = CouncilMeasurePutLinePreCheckProject.GetByBusinessID(businessId);
                    return new ApiResult { StateCode = 1, Data = project, Message = "委托成功" };
                }
                if (record.BusinessClass == "RealEstateOverallActualSurveyFlow") {
                    FlowService<RealEstateOverallActualSurveyFlow> flow = new FlowService<RealEstateOverallActualSurveyFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    RealEstateOverallActualSurveyProject project = RealEstateOverallActualSurveyProject.GetByBusinessID(businessId);
                    return new ApiResult { StateCode = 1, Data = project, Message = "委托成功" };
                } else {
                    return new ApiResult { StateCode = 0, Message = "待完善..." };
                }
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "委托失败，错误信息：" + e.Message + "" };
            }
        }

        /// <summary>
        /// 重新委托测绘单位，回到委托测绘单位环节
        /// </summary>
        /// <param name="businessId"></param>
        /// <param name="actionId"></param>
        /// <returns></returns>
        [HttpPost, Route("EntrustSurveyCompanyAgain")]
        public ApiResult EntrustSurveyCompanyAgain(string businessId, string actionId) {
            if (string.IsNullOrWhiteSpace(businessId) || string.IsNullOrWhiteSpace(actionId)) {
                return new ApiResult() { StateCode = 0, Message = "参数错误" };
            }
            //判断业务是否是自己的
            var record = service.GetById<BusinessBaseInfo>(businessId);
            if (record.CreateUserId != UserInfo.UserId) {
                return new ApiResult() { StateCode = 0, Message = "您不是此业务的创建者，不能执行此操作" };
            }
            if (record.StateCode == 2) {
                return new ApiResult() { StateCode = 0, Message = "该业务已完成，不能执行此操作" };
            }
            if (record.StateCode == 2) {
                return new ApiResult() { StateCode = 0, Message = "该业务已完成，不能执行此操作" };
            }

            var action = service.GetList<BusinessLinkInfo>("BusinessId=:id",
                    new OracleParameter(":id", OracleDbType.Varchar2) { Value = record.ID })
                .OrderByDescending(a => a.StartTime).FirstOrDefault();
            if (action == null) {
                return new ApiResult() { StateCode = 0, Message = "该业务没有流转环节，不能执行此操作" };
            }

            if (action.ID != actionId) {
                return new ApiResult() { StateCode = 0, Message = "该业务已不在当前环节，不能执行此操作" };
            }
            //todo 此处判断略微简单，必须约定ActionId为2的必须是汇交测绘成果的环节
            if (action.ActionId != 2) {
                return new ApiResult() { StateCode = 0, Message = "该业务已不在汇交测绘成果环节，不能执行此操作" };
            }

            if (action.StateCode != 0) {
                return new ApiResult() { StateCode = 0, Message = $"该业务已由【{record.SurveyCompanyName}】签收办理，不可重新委托测绘单位" };
            }

            //可以重新选择测绘单位

            if (action.StateCode == 0 && action.CurrentUserID == null && action.SignatureTime == null) {
                action.StateCode = 1;
                action.CurrentUserID = Guid.Empty.ToString("N");
                action.SignatureTime = DateTime.Now;
                service.UpdateOne(action);
            }

            var backReason = "申请人重新选择测绘单位";

            Request.WriteSDCLog("业务办理", $"撤销委托 >> 业务ID：{record.ID} >> 申请单位：{record.DeveloperName} ");
            if (record.BusinessClass == "MeasurePutLineFlow") {
                FlowService<MeasurePutLineFlow> flow = new FlowService<MeasurePutLineFlow>(service);
                flow.FlowPostToLast(record.ID, backReason);
            }
            else if (record.BusinessClass == "RealEstatePreSurveyFlow") {
                FlowService<RealEstatePreSurveyFlow> flow = new FlowService<RealEstatePreSurveyFlow>(service);
                flow.FlowPostToLast(record.ID, backReason);
            }
            else if (record.BusinessClass == "RealEstatePreCheckSurveyFlow") {
                FlowService<RealEstatePreCheckSurveyFlow> flow = new FlowService<RealEstatePreCheckSurveyFlow>(service);
                flow.FlowPostToLast(record.ID, backReason);
            }
            else if (record.BusinessClass == "BlueLineSurveyFlow") {
                FlowService<BlueLineSurveyFlow> flow = new FlowService<BlueLineSurveyFlow>(service);
                flow.FlowPostToLast(record.ID, backReason);
            }
            else if (record.BusinessClass == "MarkPointSurveyFlow") {
                FlowService<MarkPointSurveyFlow> flow = new FlowService<MarkPointSurveyFlow>(service);
                flow.FlowPostToLast(record.ID, backReason);
            }
            //else if (record.BusinessClass == "PlanCheckSurveyFlow") {
            //    FlowService<PlanCheckSurveyFlow> flow = new FlowService<PlanCheckSurveyFlow>(service);
            //    flow.FlowPostToLast(record.ID, backReason);
            //}
            else if (record.BusinessClass == "RealEstateActualSurveyFlow") {
                FlowService<RealEstateActualSurveyFlow> flow = new FlowService<RealEstateActualSurveyFlow>(service);
                flow.FlowPostToLast(record.ID, backReason);
            } else if (record.BusinessClass == "RealEstatePreSurveyBuildingTableChangeFlow") {
                FlowService<RealEstatePreSurveyBuildingTableChangeFlow> flow = new FlowService<RealEstatePreSurveyBuildingTableChangeFlow>(service);
                flow.FlowPostToLast(record.ID, backReason);
            } else if (record.BusinessClass == "RealEstatePreSurveyResultChangeFlow") {
                FlowService<RealEstatePreSurveyResultChangeFlow> flow = new FlowService<RealEstatePreSurveyResultChangeFlow>(service);
                flow.FlowPostToLast(record.ID, backReason);
            } else if (record.BusinessClass == "RealEstateActualBuildingTableChangeFlow") {
                FlowService<RealEstateActualBuildingTableChangeFlow> flow = new FlowService<RealEstateActualBuildingTableChangeFlow>(service);
                flow.FlowPostToLast(record.ID, backReason);
            } else if (record.BusinessClass == "RealEstateActualResultChangeFlow") {
                FlowService<RealEstateActualResultChangeFlow> flow = new FlowService<RealEstateActualResultChangeFlow>(service);
                flow.FlowPostToLast(record.ID, backReason);
            } else if (record.BusinessClass == "MeasurePutLinePreCheckFlow") {
                FlowService<MeasurePutLinePreCheckFlow> flow = new FlowService<MeasurePutLinePreCheckFlow>(service);
                flow.FlowPostToLast(record.ID, backReason);
            } else if (record.BusinessClass == "RealEstatePreCheckSurveyAutoFlow") {
                FlowService<RealEstatePreCheckSurveyAutoFlow> flow = new FlowService<RealEstatePreCheckSurveyAutoFlow>(service);
                flow.FlowPostToLast(record.ID, backReason);
            } else if (record.BusinessClass == "CouncilPlanCheckFlow") {
                FlowService<CouncilPlanCheckFlow> flow = new FlowService<CouncilPlanCheckFlow>(service);
                flow.FlowPostToLast(record.ID, backReason);
            } else if (record.BusinessClass == "CouncilMeasurePutLinePreCheckFlow") {
                FlowService<CouncilMeasurePutLinePreCheckFlow> flow = new FlowService<CouncilMeasurePutLinePreCheckFlow>(service);
                flow.FlowPostToLast(record.ID, backReason);
            } else if (record.BusinessClass == "RealEstateOverallActualSurveyFlow") {
                FlowService<RealEstateOverallActualSurveyFlow> flow = new FlowService<RealEstateOverallActualSurveyFlow>(service);
                flow.FlowPostToLast(record.ID, backReason);
            } else {
                return new ApiResult { StateCode = 0, Message = "操作失败，没有找到适合的业务" };
            }

            return AcceptBusiness(businessId);
        }

        /// <summary>
        /// 项目创建者主动关闭业务
        /// </summary>
        /// <param name="id"></param>
        /// <param name="closeReason"></param>
        /// <returns></returns>
        [HttpPost, Route("CloseBusinessProject")]
        public ApiResult CloseBusinessProject(string id, string closeReason) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "请输入正确的信息" };
            }

            if (string.IsNullOrWhiteSpace(closeReason)) {
                return new ApiResult { StateCode = 0, Message = "请输入关闭原因" };
            }
            var record = service.GetById<BusinessBaseInfo>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到业务信息" };
            }
            try {
                BusinessFlowActionInfo flowInfo = null;
                BusinessLinkInfoModel currentLinkInfo = null;
                //获取项目信息进行判断
                if (record.BusinessClass == "BaseSurveyDataDownloadFlow") {
                    BaseSurveyDataDownloadProject project = BaseSurveyDataDownloadProject.GetByBusinessID(id);
                    currentLinkInfo = project.CurrentAction;
                    flowInfo = project.FlowInfo.FlowActionInfo;
                }
                if (record.BusinessClass == "MeasurePutLineFlow") {
                    MeasurePutLineProject project = MeasurePutLineProject.GetByBusinessID(id);
                    currentLinkInfo = project.CurrentAction;
                    flowInfo = project.FlowInfo.FlowActionInfo;
                }
                if (record.BusinessClass == "RealEstatePreSurveyFlow") {
                    RealEstatePreSurveyProject project = RealEstatePreSurveyProject.GetByBusinessID(id);
                    currentLinkInfo = project.CurrentAction;
                    flowInfo = project.FlowInfo.FlowActionInfo;
                }
                if (record.BusinessClass == "RealEstatePreCheckSurveyFlow") {
                    RealEstatePreCheckSurveyProject project = RealEstatePreCheckSurveyProject.GetByBusinessID(id);
                    currentLinkInfo = project.CurrentAction;
                    flowInfo = project.FlowInfo.FlowActionInfo;
                }
                if (record.BusinessClass == "BlueLineSurveyFlow") {
                    BlueLineSurveyProject project = BlueLineSurveyProject.GetByBusinessID(id);
                    currentLinkInfo = project.CurrentAction;
                    flowInfo = project.FlowInfo.FlowActionInfo;
                }
                if (record.BusinessClass == "MarkPointSurveyFlow") {
                    MarkPointSurveyProject project = MarkPointSurveyProject.GetByBusinessID(id);
                    currentLinkInfo = project.CurrentAction;
                    flowInfo = project.FlowInfo.FlowActionInfo;
                }
                if (record.BusinessClass == "PlanCheckSurveyFlow") {
                    //PlanCheckSurveyProject project = PlanCheckSurveyProject.GetByBusinessID(id);
                    //currentLinkInfo = project.CurrentAction;
                    //flowInfo = project.FlowInfo.FlowActionInfo;
                }
                if (record.BusinessClass == "RealEstateActualSurveyFlow") {
                    RealEstateActualSurveyProject project = RealEstateActualSurveyProject.GetByBusinessID(id);
                    currentLinkInfo = project.CurrentAction;
                    flowInfo = project.FlowInfo.FlowActionInfo;
                }
                if (record.BusinessClass == "RealEstatePreSurveyBuildingTableChangeFlow") {
                    RealEstatePreSurveyBuildingTableChangeProject project = RealEstatePreSurveyBuildingTableChangeProject.GetByBusinessID(id);
                    currentLinkInfo = project.CurrentAction;
                    flowInfo = project.FlowInfo.FlowActionInfo;
                }
                if (record.BusinessClass == "RealEstatePreSurveyResultChangeFlow") {
                    RealEstatePreSurveyResultChangeProject project = RealEstatePreSurveyResultChangeProject.GetByBusinessID(id);
                    currentLinkInfo = project.CurrentAction;
                    flowInfo = project.FlowInfo.FlowActionInfo;
                }
                if (record.BusinessClass == "RealEstateActualBuildingTableChangeFlow") {
                    RealEstateActualBuildingTableChangeProject project = RealEstateActualBuildingTableChangeProject.GetByBusinessID(id);
                    currentLinkInfo = project.CurrentAction;
                    flowInfo = project.FlowInfo.FlowActionInfo;
                }
                if (record.BusinessClass == "RealEstateActualResultChangeFlow") {
                    RealEstateActualResultChangeProject project = RealEstateActualResultChangeProject.GetByBusinessID(id);
                    currentLinkInfo = project.CurrentAction;
                    flowInfo = project.FlowInfo.FlowActionInfo;
                }
                if (record.BusinessClass == "MeasurePutLinePreCheckFlow") {
                    MeasurePutLinePreCheckProject project = MeasurePutLinePreCheckProject.GetByBusinessID(id);
                    currentLinkInfo = project.CurrentAction;
                    flowInfo = project.FlowInfo.FlowActionInfo;
                }
                if (record.BusinessClass == "RealEstatePreCheckSurveyAutoFlow") {
                    RealEstatePreCheckSurveyAutoProject project = RealEstatePreCheckSurveyAutoProject.GetByBusinessID(id);
                    currentLinkInfo = project.CurrentAction;
                    flowInfo = project.FlowInfo.FlowActionInfo;
                }
                if (record.BusinessClass == "CouncilPlanCheckFlow") {
                    CouncilPlanCheckProject project = CouncilPlanCheckProject.GetByBusinessID(id);
                    currentLinkInfo = project.CurrentAction;
                    flowInfo = project.FlowInfo.FlowActionInfo;
                }
                if (record.BusinessClass == "CouncilMeasurePutLinePreCheckFlow") {
                    CouncilMeasurePutLinePreCheckProject project = CouncilMeasurePutLinePreCheckProject.GetByBusinessID(id);
                    currentLinkInfo = project.CurrentAction;
                    flowInfo = project.FlowInfo.FlowActionInfo;
                }
                if (record.BusinessClass == "RealEstateOverallActualSurveyFlow") {
                    RealEstateOverallActualSurveyProject project = RealEstateOverallActualSurveyProject.GetByBusinessID(id);
                    currentLinkInfo = project.CurrentAction;
                    flowInfo = project.FlowInfo.FlowActionInfo;
                }
                if (record.BusinessClass == "LandSurveyProjectFlow") {
                    LandSurveyProject project = LandSurveyProject.GetByBusinessID(id);
                    currentLinkInfo = project.CurrentAction;
                    flowInfo = project.FlowInfo.FlowActionInfo;
                }
                if (flowInfo != null && currentLinkInfo != null) {
                    //关闭业务的条件：1、业务在开始环节，2、必须是项目创建者本人才能关闭
                    if (currentLinkInfo.ActionId != flowInfo.StartActionId) {
                        return new ApiResult { StateCode = 0, Message = "关闭失败,必须在流程的第一步才能关闭业务" };
                    }
                    var userId = UserInfo.UserId;
                    if (userId != record.CreateUserId) {
                        return new ApiResult { StateCode = 0, Message = "关闭失败,必须是业务的创建者才能关闭业务" };
                    }

                    //关闭业务
                    currentLinkInfo.EndTime = DateTime.Now;
                    currentLinkInfo.StateCode = 4;
                    record.StateCode = 4;
                    record.FinishTime = DateTime.Now;
                    if (string.IsNullOrWhiteSpace(record.ExtendInfo)) {
                        record.ExtendInfo = JsonConvert.SerializeObject(new { CloseReason = closeReason });
                    }
                    else {
                        try {
                            var extendJObj = JObject.Parse(record.ExtendInfo);
                            extendJObj.Add("CloseReason");
                            record.ExtendInfo = JsonConvert.SerializeObject(extendJObj);
                        }
                        catch {
                            record.ExtendInfo = JsonConvert.SerializeObject(new { CloseReason = closeReason });
                        }
                    }

                    service.UpdateOne(BusinessLinkInfo.FromModel(currentLinkInfo));
                    service.UpdateOne(record);

                    #region 释放与工规证号的绑定
                    //预测绘
                    if (new string[] { "RealEstatePreSurveyFlow", "RealEstatePreCheckSurveyFlow", "RealEstatePreCheckSurveyAutoFlow", "RealEstatePreSurveyResultChangeFlow" }.Contains(record.BusinessClass)) {
                        var list = service.GetList<EstateProjectInfo>("PUTLINESURVEYID=:Id OR PRESURVEYID=:Id", new OracleParameter(":Id", OracleDbType.Varchar2){ Value = record.ID });
                        foreach (var item in list) {
                            item.PutLineSurveyState = SurveyState.Invalid;
                            item.PreSurveyState = SurveyState.Invalid;

                            service.UpdateOne(item);
                        }
                    }

                    #endregion

                    Request.WriteSDCLog("业务办理", $"关闭业务 >> 业务ID：{record.ID} >> 申请单位：{record.DeveloperName} ");
                    return new ApiResult { StateCode = 1, Message = "关闭成功" };
                }

                if (flowInfo == null) {
                    return new ApiResult { StateCode = 0, Message = "关闭失败,未知的业务流程" };
                }

                return new ApiResult { StateCode = 0, Message = "关闭失败,当前环节不能关闭业务" };
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "获取失败，错误信息：" + e.Message + "" };
            }
        }

        /// <summary>
        /// 测绘成果确认验收
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("AcceptSurveyProjectResult")]
        public async Task<ApiResult> AcceptSurveyProjectResult(string id, string actionId) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "请输入正确的信息" };
            }
            var baseRecord = service.GetById<BusinessBaseInfo>(id);
            if (baseRecord == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到业务信息" };
            }

            var action = service.GetById<BusinessLinkInfo>(actionId);
            if (action == null) {
                return new ApiResult { StateCode = 0, Message = "该环节不存在" };
            }

            if (action.StateCode == 2) {
                return new ApiResult { StateCode = 0, Message = "该环节已完成，请勿重复操作" };
            }

            if (action.BusinessId != id) {
                return new ApiResult { StateCode = 0, Message = "当前环节不属于该业务id" };
            }

            var userId = UserInfo.UserId;
            string cannotFlowPostMessage;
            if (!CanFlowPost(baseRecord, action, userId, out cannotFlowPostMessage)) {
                return new ApiResult { StateCode = 0, Message = $"操作失败，{cannotFlowPostMessage}" };
            }
            try {
                HttpRequestBase req = ((HttpContextBase)Request.Properties["MS_HttpContext"]).Request;
                var reqInfoHelper = new HttpRequestInfoHelper(req);
                LogService.WriteLogs(Request, "业务管理", $"成果验收 >> 业务ID：{baseRecord.ID} >> 业务类型：{baseRecord.BusinessType}", reqInfoHelper);
                if (baseRecord.BusinessClass == "MeasurePutLineFlow") {
                    FlowService<MeasurePutLineFlow> flow = new FlowService<MeasurePutLineFlow>(service);
                    flow.FlowPostToNext(id);
                    MeasurePutLineProject project = MeasurePutLineProject.GetByBusinessID(id);
                    var content = project.ContentInfo;
                    if (content != null) {
                        var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(content.ProjectPlanPermission);
                        BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectPutlineSurveyStates(plans, content.ID, SurveyState.CompleteSurvey, null));
                    }
                    return new ApiResult { StateCode = 1, Data = project, Message = "验收成功" };
                }
                if (baseRecord.BusinessClass == "RealEstatePreSurveyFlow") {
                    FlowService<RealEstatePreSurveyFlow> flow = new FlowService<RealEstatePreSurveyFlow>(service);
                    flow.FlowPostToNext(id);
                    RealEstatePreSurveyProject project = RealEstatePreSurveyProject.GetByBusinessID(id);
                    var content = project.ContentInfo;
                    if (content != null) {
                        var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(content.ProjectPlanPermission);
                        BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectPreSurveyStates(plans, content.ID, SurveyState.CompleteSurvey, null));
                    }
                    return new ApiResult { StateCode = 1, Data = project, Message = "验收成功" };
                }
                if (baseRecord.BusinessClass == "RealEstatePreCheckSurveyFlow") {
                    FlowService<RealEstatePreCheckSurveyFlow> flow = new FlowService<RealEstatePreCheckSurveyFlow>(service);
                    flow.FlowPostToNext(id);
                    RealEstatePreCheckSurveyProject project = RealEstatePreCheckSurveyProject.GetByBusinessID(id);
                    var ps_content = project.PreSurveyContentInfo;
                    if (ps_content != null) {
                        var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(ps_content.ProjectPlanPermission);
                        BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectPreSurveyStates(plans, ps_content.ID, SurveyState.CompleteSurvey, null));
                    }
                    var pl_content = project.PutLineContentInfo;
                    if (pl_content != null) {
                        var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(pl_content.ProjectPlanPermission);
                        BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectPreSurveyStates(plans, pl_content.ID, SurveyState.CompleteSurvey, null));
                    }
                    return new ApiResult { StateCode = 1, Data = project, Message = "验收成功" };
                }
                if (baseRecord.BusinessClass == "BlueLineSurveyFlow") {
                    FlowService<BlueLineSurveyFlow> flow = new FlowService<BlueLineSurveyFlow>(service);
                    flow.FlowPostToNext(id);
                    BlueLineSurveyProject project = BlueLineSurveyProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "验收成功" };
                }
                if (baseRecord.BusinessClass == "MarkPointSurveyFlow") {
                    FlowService<MarkPointSurveyFlow> flow = new FlowService<MarkPointSurveyFlow>(service);
                    flow.FlowPostToNext(id);
                    MarkPointSurveyProject project = MarkPointSurveyProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "验收成功" };
                }
                if (baseRecord.BusinessClass == "RealEstateActualSurveyFlow") {
                    FlowService<RealEstateActualSurveyFlow> flow = new FlowService<RealEstateActualSurveyFlow>(service);
                    flow.FlowPostToNext(id);
                    RealEstateActualSurveyProject project = await RealEstateActualSurveyProject.GetByBusinessID(id, true);
                    var content = project.ContentInfo;
                    if (content != null) {
                        var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(content.ProjectPlanPermission);
                        BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectRealSurveyStates(plans, content.ID, SurveyState.CompleteSurvey, null));
                    }

                    //通知云平台完成替换MDB
                    BackgroundJob.Enqueue(() => XCloudService.SetFinishReplaceMDB(id, baseRecord.BusinessClass, null));

                    return new ApiResult { StateCode = 1, Data = project, Message = "验收成功" };
                }
                if (baseRecord.BusinessClass == "RealEstatePreSurveyBuildingTableChangeFlow") {
                    FlowService<RealEstatePreSurveyBuildingTableChangeFlow> flow = new FlowService<RealEstatePreSurveyBuildingTableChangeFlow>(service);
                    flow.FlowPostToNext(id);
                    RealEstatePreSurveyBuildingTableChangeProject project = await RealEstatePreSurveyBuildingTableChangeProject.GetByBusinessID(id, true);
                    var content = project.ContentInfo;
                    if (content != null) {
                        var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(content.ProjectPlanPermission);
                        BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectPreSurveyStates(plans, content.ID, SurveyState.CompleteSurvey, null));
                    }
                    return new ApiResult { StateCode = 1, Data = project, Message = "验收成功" };
                }
                if (baseRecord.BusinessClass == "RealEstatePreSurveyResultChangeFlow") {
                    FlowService<RealEstatePreSurveyResultChangeFlow> flow = new FlowService<RealEstatePreSurveyResultChangeFlow>(service);
                    flow.FlowPostToNext(id);
                    RealEstatePreSurveyResultChangeProject project = await RealEstatePreSurveyResultChangeProject.GetByBusinessID(id, true);
                    var content = project.ContentInfo;
                    if (content != null) {
                        var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(content.ProjectPlanPermission);
                        BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectPreSurveyStates(plans, content.ID, SurveyState.CompleteSurvey, null));
                    }
                    return new ApiResult { StateCode = 1, Data = project, Message = "验收成功" };
                }
                if (baseRecord.BusinessClass == "RealEstateActualBuildingTableChangeFlow") {
                    FlowService<RealEstateActualBuildingTableChangeFlow> flow = new FlowService<RealEstateActualBuildingTableChangeFlow>(service);
                    flow.FlowPostToNext(id);
                    RealEstateActualBuildingTableChangeProject project = RealEstateActualBuildingTableChangeProject.GetByBusinessID(id);
                    var content = project.ContentInfo;
                    if (content != null) {
                        var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(content.ProjectPlanPermission);
                        BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectRealSurveyStates(plans, content.ID, SurveyState.CompleteSurvey, null));
                    }
                    return new ApiResult { StateCode = 1, Data = project, Message = "验收成功" };
                }
                if (baseRecord.BusinessClass == "RealEstateActualResultChangeFlow") {
                    FlowService<RealEstateActualResultChangeFlow> flow = new FlowService<RealEstateActualResultChangeFlow>(service);
                    flow.FlowPostToNext(id);
                    RealEstateActualResultChangeProject project = await RealEstateActualResultChangeProject.GetByBusinessID(id, true);
                    var content = project.ContentInfo;
                    if (content != null) {
                        var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(content.ProjectPlanPermission);
                        BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectRealSurveyStates(plans, content.ID, SurveyState.CompleteSurvey, null));
                    }
                    return new ApiResult { StateCode = 1, Data = project, Message = "验收成功" };
                }
                if (baseRecord.BusinessClass == "MeasurePutLinePreCheckFlow") {
                    FlowService<MeasurePutLinePreCheckFlow> flow = new FlowService<MeasurePutLinePreCheckFlow>(service);
                    flow.FlowPostToNext(id);
                    MeasurePutLinePreCheckProject project = MeasurePutLinePreCheckProject.GetByBusinessID(id);
                    var content = project.ContentInfo;
                    if (content != null) {
                        var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(content.ProjectPlanPermission);
                        BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectPutlineSurveyStates(plans, content.ID, SurveyState.CompleteSurvey, null));
                    }
                    return new ApiResult { StateCode = 1, Data = project, Message = "验收成功" };
                }
                if (baseRecord.BusinessClass == "RealEstatePreCheckSurveyAutoFlow") {
                    FlowService<RealEstatePreCheckSurveyAutoFlow> flow = new FlowService<RealEstatePreCheckSurveyAutoFlow>(service);
                    flow.FlowPostToNext(id);

                    
                    RealEstatePreCheckSurveyAutoProject project = RealEstatePreCheckSurveyAutoProject.GetByBusinessID(id);

                    //更新项目表状态
                    List<Tuple<SurveyClass, List<ProjectPlanPermissionInfo>>> plansList =
                        new List<Tuple<SurveyClass, List<ProjectPlanPermissionInfo>>>();

                    var ps_content = project.PreSurveyContentInfo;
                    if (ps_content != null) {
                        var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(ps_content.ProjectPlanPermission);
                        //BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectPreSurveyStates(plans, ps_content.ID, SurveyState.CompleteSurvey, null));
                        plansList.Add(new Tuple<SurveyClass, List<ProjectPlanPermissionInfo>>(SurveyClass.PreSurvey, plans));
                    }
                    var pl_content = project.PutLineContentInfo;
                    if (pl_content != null) {
                        var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(pl_content.ProjectPlanPermission);
                        //BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectPreSurveyStates(plans, pl_content.ID, SurveyState.CompleteSurvey, null));
                        plansList.Add(new Tuple<SurveyClass, List<ProjectPlanPermissionInfo>>(SurveyClass.Putline, plans));
                    }
                    BackgroundJob.Enqueue(() => EstateProjectInfo.BatchSaveEstateProjectSurveyStates(id, plansList, SurveyState.CompleteSurvey, null));

                    //如果是2023-08-30至2024-12-31发证的工规证号，并且未缴纳城建配套费，就要走人工审核
                    if (plansList.Any(s => s.Item2.Any(q => q.NeedManualAudit == true))) {
                        return new ApiResult {StateCode = 1, Data = project, Message = "验收成功"};
                    }

                    //key:AUDITSTATUS
                    //value:0（无需人工审核），1（情景1），2（情景2），1和2就要走人工审核
                    if (ps_content?.BuildingTableInfo?.Contains("AUDITSTATUS") == true) {
                        var buildingTableInfo = JsonConvert.DeserializeObject<JArray>(ps_content.BuildingTableInfo);
                        if (buildingTableInfo.Any(s => 
                            s["AUDITSTATUS"]?.ToObject<int?>() == 1 || s["AUDITSTATUS"]?.ToObject<int?>() == 2)) {
                            return new ApiResult {StateCode = 1, Data = project, Message = "验收成功"};
                        }
                    }

                    #region 完成自动签收

                    EstateProjectAuditController controller = new EstateProjectAuditController();

                    //签收业务
                    var result = controller.SignProjectAudit(id);
                    if (result.StateCode == 0) {
                        result.Message = $"签收失败：{result.Message}";
                        return result;
                    }

                    #endregion

                    #region 调用云平台接口通知业务自动办理
                    Hangfire.BackgroundJob.Enqueue(() => XCloudService.ToXCloudCreateBusiness(id, baseRecord.BusinessClass, null));
                    #endregion

                    return new ApiResult { StateCode = 1, Data = project, Message = "验收成功" };
                }
                if (baseRecord.BusinessClass == "CouncilPlanCheckFlow") {
                    FlowService<CouncilPlanCheckFlow> flow = new FlowService<CouncilPlanCheckFlow>(service);
                    flow.FlowPostToNext(id);
                    CouncilPlanCheckProject project = await CouncilPlanCheckProject.GetByBusinessID(id, true);
                    var content = project.ContentInfo;
                    if (content != null) {
                        var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(content.ProjectPlanPermission);
                        BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectRealSurveyStates(plans, content.ID, SurveyState.CompleteSurvey, null));
                    }

                    //通知云平台完成替换MDB
                    BackgroundJob.Enqueue(() => XCloudService.SetFinishReplaceMDB(id, baseRecord.BusinessClass, null));

                    return new ApiResult { StateCode = 1, Data = project, Message = "验收成功" };
                }
                if (baseRecord.BusinessClass == "CouncilMeasurePutLinePreCheckFlow") {
                    FlowService<CouncilMeasurePutLinePreCheckFlow> flow = new FlowService<CouncilMeasurePutLinePreCheckFlow>(service);
                    flow.FlowPostToNext(id);
                    CouncilMeasurePutLinePreCheckProject project = CouncilMeasurePutLinePreCheckProject.GetByBusinessID(id);
                    var content = project.ContentInfo;
                    if (content != null) {
                        var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(content.ProjectPlanPermission);
                        BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectPutlineSurveyStates(plans, content.ID, SurveyState.CompleteSurvey, null));
                    }

                    #region 完成自动签收

                    EstateProjectAuditController controller = new EstateProjectAuditController();

                    //签收业务
                    var result = controller.SignProjectAudit(id);
                    if (result.StateCode == 0) {
                        result.Message = $"签收失败：{result.Message}";
                        return result;
                    }

                    #endregion

                    #region 调用云平台接口通知业务自动办理
                    Hangfire.BackgroundJob.Enqueue(() => XCloudService.ToXCloudCreateBusiness(id, baseRecord.BusinessClass, null));
                    #endregion

                    return new ApiResult { StateCode = 1, Data = project, Message = "验收成功" };
                }
                if (baseRecord.BusinessClass == "RealEstateOverallActualSurveyFlow") {
                    FlowService<RealEstateOverallActualSurveyFlow> flow = new FlowService<RealEstateOverallActualSurveyFlow>(service);
                    flow.FlowPostToNext(id);
                    RealEstateOverallActualSurveyProject project = await RealEstateOverallActualSurveyProject.GetByBusinessID(id, true);
                    var content = project.ContentInfo;
                    if (content != null) {
                        var plans = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(content.ProjectPlanPermission);
                        BackgroundJob.Enqueue(() => EstateProjectInfo.SaveEstateProjectRealSurveyStates(plans, content.ID, SurveyState.CompleteSurvey, null));
                    }

                    //通知云平台完成替换MDB
                    BackgroundJob.Enqueue(() => XCloudService.SetFinishReplaceMDB(id, baseRecord.BusinessClass, null));

                    return new ApiResult { StateCode = 1, Data = project, Message = "验收成功" };
                }
                return new ApiResult { StateCode = 0, Data = "", Message = "...待完善" };
            }
            catch (Exception e) {
                return new ApiResult() { StateCode = 0, Message = "验收失败，错误信息：" + e.GetStackTraces() + "" };
            }
        }

        /// <summary>
        /// 根据宗地号获取楼盘表信息
        /// </summary>
        /// <param name="zdh">宗地号</param>
        /// <param name="chzt">测绘状态：1预测绘；2实测绘</param>
        /// <returns></returns>
        [HttpGet, Route("GetBuildingTableByZDH")]
        public async Task<ApiResult> GetBuildingTableByZDH(string zdh, int chzt = 0) {
            var uri = new Uri(ExternalApiConfig.BDCSystemApiUrl);
            uri = new Uri(uri, $"/api/bdcquery/GetBuildingTableByZDH?zdh={zdh}&chzt={chzt}");
            var httpClient = HttpClientFactory.Create();
            var responseMessage = await httpClient.GetAsync(uri.ToString());
            if (responseMessage.StatusCode == HttpStatusCode.OK) {
                var response = responseMessage.Content != null ? await responseMessage.Content.ReadAsStringAsync() : null;
                var jObj = JsonConvert.DeserializeObject<JObject>(response);
                return new ApiResult() {
                    StateCode = jObj["IsSuccess"].ToObject<bool?>() == true ? 1 : 0,
                    Data = jObj["Data"],
                    Message = jObj["Message"]?.ToString()
                };
            }
            return new ApiResult() {
                StateCode = 0,
                Message = "查询失败，请联系管理员"
            };
        }

        /// <summary>
        /// 获取幢状态信息
        /// </summary>
        /// <returns></returns>
        [HttpPost, Route("GetZRZZT")]
        public async Task<ApiResult> GetZRZZT() {
            JObject jObject = JObject.Parse(await Request.Content.ReadAsStringAsync());

            string businessClass = jObject["BusinessClass"]?.ToString();
            JArray zrzArr = JArray.FromObject(jObject["Data"]);

            if (string.IsNullOrWhiteSpace(businessClass)) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "businessClass参数无效"
                };
            }

            if (!zrzArr.Any()) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "Data参数无效"
                };
            }

            var list = zrzArr.Select(s => new Tuple<string, string>(s["ZRZGUID"].ToString(), s["ZL"].ToString()))
                .ToList();

            var zrzguid = list.Select(s => s.Item1).ToArray();

            //校验是否有正在办理的业务
            var useResult = CanZRZUse(null, businessClass, list);
            if (useResult.Any()) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = string.Join("；", useResult)
                };
            }

            //外网流程不做以下限制，由内网决定
            return new ApiResult {
                StateCode = 1
            };

            //校验幢guid是否可用
            var result = await GetZRZZT(string.Join(",", zrzguid));
            if (!string.IsNullOrWhiteSpace(result.Item1)) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = result.Item1
                };
            }
            else {
                List<JObject> fails = new List<JObject>();
                foreach (var item in result.Item2) {
                    if (item["SUCESS"].ToObject<bool>() == false) {
                        fails.Add(JObject.FromObject(new {
                            @zrzguid = item["ZRZGUID"].ToString(),
                            @zl = zrzArr.FirstOrDefault(s => s["ZRZGUID"].ToString() == item["ZRZGUID"].ToString())["ZL"].ToString(),
                            @msg = GetZRZZTDescription(item["CODE"].ToObject<int>())
                        }));
                    }
                }
                return new ApiResult() {
                    StateCode = fails.Any() ? 0 : 1,
                    Message = fails.Any() ? string.Join("；", fails.Select(s => $"[{s["zl"].ToString()}]{s["msg"].ToString()}")) : ""
                }; 
            }
        }
        #endregion

        #region 测绘单位相关接口

        /// <summary>
        /// 蓝线图、拨地定桩汇交成果
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("PostAchievementResult")]
        public ApiResult PostAchievementResult(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "请输入正确的信息" };
            }
            var record = service.GetById<BusinessBaseInfo>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到业务信息" };
            }
            try {
                HttpFileCollection files = System.Web.HttpContext.Current.Request.Files;
                //判断是否有文件上传
                if (files.Count == 0) {
                    return new ApiResult { StateCode = 0, Message = "提交失败，请选择要上传的文件" };
                }

                Request.WriteSDCLog("业务办理", $"开始上传测绘成果前清空原有的数据 >> 蓝线图、拨地定桩 >> 业务ID：{record.ID} >> 测绘单位：{record.SurveyCompanyName} ");

                #region 清空原来的数据，避免使用的是上一次的检查通过的结果

                if (record.BusinessClass == "BlueLineSurveyFlow") {
                    //更新检查信息至contentinfo
                    BlueLineSurveyContentInfo content = service.GetById<BlueLineSurveyContentInfo>(record.ID);
                    content.AchievementCheckID = null;
                    content.AchievementCheckState = 0;
                    content.SurveyMasterName = null;
                    content.SurveyMasterNo = null;
                    content.SurveyMasterSureTime = null;
                    service.UpdateOne(content);
                }
                if (record.BusinessClass == "MarkPointSurveyFlow") {
                    MarkPointSurveyContentInfo content = service.GetById<MarkPointSurveyContentInfo>(record.ID);
                    content.AchievementCheckID = null;
                    content.AchievementCheckState = 0;
                    content.SurveyMasterName = null;
                    content.SurveyMasterNo = null;
                    content.SurveyMasterSureTime = null;
                    service.UpdateOne(content);
                }
                //成果附件逻辑删除
                var attachments = service.GetList<AttachmentInfo>($"BusinessID='{record.ID}' AND AttachmentType = '项目成果附件' AND AttachmentExt!='.pdf'");
                if (attachments.Any()) {
                    foreach (var item in attachments) {
                        if (item.StateCode != 1) {
                            item.StateCode = 1;
                            service.UpdateOne(item);
                        }
                    }
                }
                #endregion

                Stream stream = files[0].InputStream;//new MemoryStream();
                byte[] bytes = new byte[stream.Length];
                stream.Read(bytes, 0, bytes.Length);
                stream.Flush();
                stream.Close();
                stream.Dispose();

                string strByte64 = Convert.ToBase64String(bytes);
                var strByte64Encoded = LongUrlEncode(strByte64);

                Request.WriteSDCLog("业务办理", $"开始上传测绘成果到EPS接口 >> 蓝线图、拨地定桩 >> 业务ID：{record.ID} >> 测绘单位：{record.SurveyCompanyName} ");

                var servicesUrl = ExternalApiConfig.EPSSDCServices + "/RunEpsInterface";
                string par = $"ID={id}&strYWMC={HttpUtility.UrlEncode(record.BusinessType)}&strDownloadLayer=&strByte64={strByte64Encoded}&strFileType=mdb";
                string responseString = "";
                bytes = Encoding.UTF8.GetBytes(par);
                HttpWebRequest request = WebRequest.Create(servicesUrl) as HttpWebRequest;
                request.Method = "POST";
                request.ContentType = "application/x-www-form-urlencoded";
                request.ContentLength = (long)bytes.Length;
                using (Stream requestStream = request.GetRequestStream()) {
                    requestStream.Write(bytes, 0, bytes.Length);
                    requestStream.Flush();
                    requestStream.Close();
                }
                WebResponse rs = request.GetResponse();
                using (HttpWebResponse response = request.GetResponse() as HttpWebResponse) {
                    StreamReader reader = new StreamReader(response.GetResponseStream());
                    responseString = reader.ReadToEnd();
                    reader.Close();
                }
                System.Xml.XmlDocument xml = new System.Xml.XmlDocument();
                xml.LoadXml(responseString);
                JObject result = (JObject)JsonConvert.DeserializeObject(xml.InnerText);
                string fileId = result["FILEID"]?.Value<string>();
                if (string.IsNullOrWhiteSpace(fileId)) {
                    //EPS接口有异常，仅返回status信息
                    return new ApiResult() { StateCode = 0, Message = $"提交失败，数据检查接口异常，接口返回消息为：{result["status"].Value<string>()}" };
                }

                Request.WriteSDCLog("业务办理", $"上传测绘成果到EPS接口成功 >> 蓝线图、拨地定桩 >> 业务ID：{record.ID} >> 测绘单位：{record.SurveyCompanyName} ");

                if (record.BusinessClass == "BlueLineSurveyFlow") {
                    //更新检查信息至contentinfo
                    BlueLineSurveyContentInfo content = service.GetById<BlueLineSurveyContentInfo>(record.ID);
                    content.AchievementCheckID = fileId;
                    content.AchievementCheckState = 0;
                    content.SurveyMasterName = null;
                    content.SurveyMasterNo = null;
                    content.SurveyMasterSureTime = null;
                    service.UpdateOne(content);
                }
                if (record.BusinessClass == "MarkPointSurveyFlow") {
                    MarkPointSurveyContentInfo content = service.GetById<MarkPointSurveyContentInfo>(record.ID);
                    content.AchievementCheckID = fileId;
                    content.AchievementCheckState = 0;
                    content.SurveyMasterName = null;
                    content.SurveyMasterNo = null;
                    content.SurveyMasterSureTime = null;
                    service.UpdateOne(content);
                }
                //成果附件逻辑删除
                attachments = service.GetList<AttachmentInfo>($"BusinessID='{record.ID}' AND AttachmentType = '项目成果附件' AND AttachmentExt!='.pdf'");
                if (attachments.Any()) {
                    foreach (var item in attachments) {
                        if (item.StateCode != 1) {
                            item.StateCode = 1;
                            service.UpdateOne(item);
                        }
                    }
                }
                //启动异步作业进行检测
                BackgroundJob.Schedule(() => SurveyResultJob.LoadAchievementCheckState(fileId, record.ID, record.BusinessType, null), TimeSpan.FromMinutes(1));
                Request.WriteSDCLog("业务办理", $"提交测绘成果 >> 蓝线图、拨地定桩 >> 业务ID：{record.ID} >> 测绘单位：{record.SurveyCompanyName} ");
                return new ApiResult() { StateCode = 1, Data = fileId, Message = "提交成功" };
            }
            catch (Exception e) {
                Request.WriteSDCLog("业务办理", $"提交测绘成果异常 >> 蓝线图、拨地定桩 >> 业务ID：{record.ID} >> 测绘单位：{record.SurveyCompanyName}。异常信息：{e.GetStackTraces()} ");
                return new ApiResult() { StateCode = 0, Message = "提交失败，错误信息：" + e.Message + "" };
            }

        }
        /// <summary>
        /// 提交测绘成果到多测合一系统
        /// </summary>
        /// <param name="id">业务ID</param>
        /// <returns>当StateCode为2时，说明成果进入了检查程序，Data返回的则为成果检查状态的参数
        /// 当StateCode为1时，重新读取业务信息，则可以从ContentInfo中得到测绘成果信息，从AttachmentInfo中得到成果附件
        /// </returns>
        [HttpPost, Route("PostSurveyProjectResult")]
        public async Task<ApiResult> PostSurveyProjectResult(string id) {
            /* 放线测量不需要检查，直接按上传成果附件处理
             * 不动产预测绘需要调用EPS检查数据接口
             */
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "请输入正确的信息" };
            }
            var record = service.GetById<BusinessBaseInfo>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到业务信息" };
            }
            try {
                //--question  暂时只有预测绘数据检查需要调用此接口，是否不需要进行业务类型判断
                //--answer    预留判断，实测绘成果到时候也需要调用此接口
                HttpFileCollection files = System.Web.HttpContext.Current.Request.Files;
                //判断是否有文件上传
                if (files.Count == 0) {
                    return new ApiResult { StateCode = 0, Message = "提交失败，请选择要上传的文件" };
                }

                ////数据压缩
                //byte[] compressBuffer = null;
                //var ms = new MemoryStream();
                //using (var zs = new GZipStream(ms, CompressionMode.Compress, true)) {
                //    zs.Write(bytes,0,bytes.Length);
                //}

                //compressBuffer = ms.ToArray();

                Stream stream = files[0].InputStream;//new MemoryStream();
                byte[] bytes = new byte[stream.Length];
                stream.Read(bytes, 0, bytes.Length);
                stream.Flush();
                stream.Close();
                stream.Dispose();

                string responseString = "";
                string fileId = "";
                //全面核实业务
                if (record.BusinessClass == nameof(RealEstateOverallActualSurveyFlow)) {
                    Request.WriteSDCLog("业务办理", $"开始上传测绘成果前清空原有的数据 >> 预核、实核 >> 业务ID：{record.ID} >> 测绘单位：{record.SurveyCompanyName} ");

                    #region 清空原来的数据，避免使用的是上一次的检查通过的结果

                    if (record.BusinessClass == nameof(RealEstateOverallActualSurveyFlow)){
                        //更新检查信息至contentinfo
                        EstateActualSurveyContentInfo content = service.GetById<EstateActualSurveyContentInfo>(record.ID);
                        content.DataCheckID = null;
                        content.DataCheckState = 0;
                        //如果是重新上传成果，则需要先移除原来的成果
                        content.BuildingTableInfo = null;
                        content.ProjectResultInfo = null;
                        content.SurveyMasterName = null;
                        content.SurveyMasterNo = null;
                        content.SurveyMasterSureTime = null;
                        service.UpdateOne(content);
                    }

                    //成果附件逻辑删除
                    var attachments1 = service.GetList<AttachmentInfo>($"BusinessID='{record.ID}' AND AttachmentType = '项目成果附件' AND AttachmentExt='.mdb'");
                    if (attachments1.Any()) {
                        foreach (var item in attachments1) {
                            if (item.StateCode != 1) {
                                item.StateCode = 1;
                                service.UpdateOne(item);
                            }
                        }
                    }

                    #endregion

                    string servicesUrl = $"{EPSOverallUploadMDBUrl}?ID={id}&strYWMC={HttpUtility.UrlEncode("全面核实")}&strDownloadLayer=&strFileType=mdb";

                    var dataContent = new ByteArrayContent(bytes);
                    dataContent.Headers.ContentDisposition = new ContentDispositionHeaderValue("form-data") {
                        Name = "file",
                        FileName = files[0].FileName,
                    };

                    using (var content = new MultipartFormDataContent()) {
                        string boundary = string.Format("--{0}", DateTime.Now.Ticks.ToString("x"));
                        content.Headers.Add("ContentType", $"multipart/form-data, boundary={boundary}");

                        content.Add(dataContent);

                        var httpClient = HttpClientFactory.Create();
                        var responseMessage = await httpClient.PostAsync(servicesUrl, content);
                        if (responseMessage.IsSuccessStatusCode) {
                            responseString = await responseMessage.Content.ReadAsStringAsync();
                            
                            var jObj = JsonConvert.DeserializeObject<JObject>(responseString.Trim('"').Replace("\\\"", "\""));

                            fileId = jObj["StrFileGuid"]?.ToString();

                            if (string.IsNullOrWhiteSpace(fileId)) {
                                //EPS接口有异常，仅返回status信息
                                return new ApiResult() { StateCode = 0, Message = $"提交失败，数据检查接口异常，接口返回消息为：{jObj["Msg"].Value<string>()}" };
                            }
                        }
                        else {
                            return new ApiResult() {
                                StateCode = 0,
                                Message = $"上传接口返回状态失败：{(int)responseMessage.StatusCode}"
                            };
                        }
                    }
                }
                else {

                    //string strByte64 = Convert.ToBase64String(compressBuffer);
                    string strByte64 = Convert.ToBase64String(bytes);
                    //var strByte64Encoded = HttpUtility.UrlEncode(LongUrlEncode(strByte64));
                    var strByte64Encoded = LongUrlEncode(strByte64);

                    var servicesUrl = ExternalApiConfig.EPSSDCServices + "/RunEpsInterface";
                    string type = null;
                    switch (record.BusinessClass) {
                        case nameof(RealEstatePreSurveyFlow):
                        case nameof(RealEstatePreCheckSurveyFlow):
                        case nameof(RealEstatePreSurveyResultChangeFlow):
                        case nameof(RealEstatePreCheckSurveyAutoFlow):
                            type = "不动产预测绘";
                            break;
                        case nameof(RealEstateActualSurveyFlow):
                        case nameof(RealEstateActualResultChangeFlow):
                            type = "不动产实核测绘";
                            break;
                        case nameof(CouncilPlanCheckFlow):
                            type = "市政工程";
                            break;
                        default:
                            return new ApiResult { StateCode = 0, Message = "业务类型不正确" };
                    }

                    Request.WriteSDCLog("业务办理", $"开始上传测绘成果前清空原有的数据 >> 预核、实核 >> 业务ID：{record.ID} >> 测绘单位：{record.SurveyCompanyName} ");

                    #region 清空原来的数据，避免使用的是上一次的检查通过的结果

                    if (record.BusinessClass == "RealEstatePreSurveyFlow" 
                        || record.BusinessClass == "RealEstatePreCheckSurveyFlow"
                        || record.BusinessClass == "RealEstatePreSurveyResultChangeFlow"
                        || record.BusinessClass == "RealEstatePreCheckSurveyAutoFlow"
                        ) {
                        //更新检查信息至contentinfo
                        PreSurvey_ContentInfo content = service.GetById<PreSurvey_ContentInfo>(record.ID);
                        content.DataCheckID = null;
                        content.DataCheckState = 0;
                        //如果是重新上传成果，则需要先移除原来的成果
                        content.BuildingTableInfo = null;
                        content.ProjectResultInfo = null;
                        content.SurveyMasterName = null;
                        content.SurveyMasterNo = null;
                        content.SurveyMasterSureTime = null;

                        service.UpdateOne(content);
                    }


                    if (record.BusinessClass == "RealEstateActualSurveyFlow" 
                        || record.BusinessClass == "RealEstateActualResultChangeFlow"
                       ) {
                        //更新检查信息至contentinfo
                        EstateActualSurveyContentInfo content = service.GetById<EstateActualSurveyContentInfo>(record.ID);
                        content.DataCheckID = null;
                        content.DataCheckState = 0;
                        //如果是重新上传成果，则需要先移除原来的成果
                        content.BuildingTableInfo = null;
                        content.ProjectResultInfo = null;
                        content.SurveyMasterName = null;
                        content.SurveyMasterNo = null;
                        content.SurveyMasterSureTime = null;
                        service.UpdateOne(content);
                    }

                    if (record.BusinessClass == "CouncilPlanCheckFlow") {
                        //更新检查信息至contentinfo
                        CouncilPlanCheckContentInfo content = service.GetById<CouncilPlanCheckContentInfo>(record.ID);
                        content.DataCheckID = null;
                        content.DataCheckState = 0;
                        //如果是重新上传成果，则需要先移除原来的成果
                        content.BuildingTableInfo = null;
                        content.ProjectResultInfo = null;
                        content.SurveyMasterName = null;
                        content.SurveyMasterNo = null;
                        content.SurveyMasterSureTime = null;
                        service.UpdateOne(content);
                    }


                    //成果附件逻辑删除
                    var attachments1 = service.GetList<AttachmentInfo>($"BusinessID='{record.ID}' AND AttachmentType = '项目成果附件' AND AttachmentExt='.mdb'");
                    if (attachments1.Any()) {
                        foreach (var item in attachments1) {
                            if (item.StateCode != 1) {
                                item.StateCode = 1;
                                service.UpdateOne(item);
                            }
                        }
                    }

                    #endregion

                    Request.WriteSDCLog("业务办理", $"开始上传测绘成果到EPS接口 >> 预核、实核 >> 业务ID：{record.ID} >> 测绘单位：{record.SurveyCompanyName} ");


                    string par = $"ID={id}&strYWMC={HttpUtility.UrlEncode(type)}&strDownloadLayer=&strByte64={strByte64Encoded}&strFileType=mdb";
                    
                    bytes = Encoding.UTF8.GetBytes(par);
                    HttpWebRequest request = WebRequest.Create(servicesUrl) as HttpWebRequest;
                    request.Method = "POST";
                    request.ContentType = "application/x-www-form-urlencoded";
                    request.ContentLength = (long)bytes.Length;
                    using (Stream requestStream = request.GetRequestStream()) {
                        requestStream.Write(bytes, 0, bytes.Length);
                        requestStream.Flush();
                        requestStream.Close();
                    }

                    using (HttpWebResponse response = request.GetResponse() as HttpWebResponse) {
                        StreamReader reader = new StreamReader(response.GetResponseStream());
                        responseString = reader.ReadToEnd();
                        reader.Close();
                    }

                    System.Xml.XmlDocument xml = new System.Xml.XmlDocument();
                    xml.LoadXml(responseString);
                    JObject result = (JObject)JsonConvert.DeserializeObject(xml.InnerText);
                    fileId = result["FILEID"]?.Value<string>();
                    if (string.IsNullOrWhiteSpace(fileId)) {
                        //EPS接口有异常，仅返回status信息
                        return new ApiResult() { StateCode = 0, Message = $"提交失败，数据检查接口异常，接口返回消息为：{result["status"].Value<string>()}" };
                    }
                }

                if (record.BusinessClass == "RealEstatePreSurveyFlow" 
                    || record.BusinessClass == "RealEstatePreCheckSurveyFlow"
                    || record.BusinessClass == "RealEstatePreSurveyResultChangeFlow"
                    || record.BusinessClass == "RealEstatePreCheckSurveyAutoFlow"
                   ) {
                    //更新检查信息至contentinfo
                    PreSurvey_ContentInfo content = service.GetById<PreSurvey_ContentInfo>(record.ID);
                    content.DataCheckID = fileId;
                    content.DataCheckState = 0;
                    //如果是重新上传成果，则需要先移除原来的成果
                    content.BuildingTableInfo = null;
                    content.ProjectResultInfo = null;
                    content.SurveyMasterName = null;
                    content.SurveyMasterNo = null;
                    content.SurveyMasterSureTime = null;

                    service.UpdateOne(content);
                }


                if (record.BusinessClass == "RealEstateActualSurveyFlow" 
                    || record.BusinessClass == "RealEstateActualResultChangeFlow"
                    || record.BusinessClass == "RealEstateOverallActualSurveyFlow"
                ) {
                    //更新检查信息至contentinfo
                    EstateActualSurveyContentInfo content = service.GetById<EstateActualSurveyContentInfo>(record.ID);
                    content.DataCheckID = fileId;
                    content.DataCheckState = 0;
                    //如果是重新上传成果，则需要先移除原来的成果
                    content.BuildingTableInfo = null;
                    content.ProjectResultInfo = null;
                    content.SurveyMasterName = null;
                    content.SurveyMasterNo = null;
                    content.SurveyMasterSureTime = null;
                    service.UpdateOne(content);
                }

                if (record.BusinessClass == "CouncilPlanCheckFlow") {
                    //更新检查信息至contentinfo
                    CouncilPlanCheckContentInfo content = service.GetById<CouncilPlanCheckContentInfo>(record.ID);
                    content.DataCheckID = fileId;
                    content.DataCheckState = 0;
                    //如果是重新上传成果，则需要先移除原来的成果
                    content.BuildingTableInfo = null;
                    content.ProjectResultInfo = null;
                    content.SurveyMasterName = null;
                    content.SurveyMasterNo = null;
                    content.SurveyMasterSureTime = null;
                    service.UpdateOne(content);
                }


                //成果附件逻辑删除
                var attachments = service.GetList<AttachmentInfo>($"BusinessID='{record.ID}' AND AttachmentType = '项目成果附件' AND AttachmentExt='.mdb'");
                if (attachments.Any()) {
                    foreach (var item in attachments) {
                        if (item.StateCode != 1) {
                            item.StateCode = 1;
                            service.UpdateOne(item);
                        }
                    }
                }


                //启动异步作业进行检测
                BackgroundJob.Schedule(() => SurveyResultJob.LoadSurveyResultCheckState(fileId, record.ID, null), TimeSpan.FromMinutes(1));

                Request.WriteSDCLog("业务办理", $"提交测绘成果 >> 预核、实核 >> 业务ID：{record.ID} >> 测绘单位：{record.SurveyCompanyName} ");
                return new ApiResult() { StateCode = 1, Data = fileId, Message = "提交成功" };
            }
            catch (Exception e) {
                Request.WriteSDCLog("业务办理", $"提交测绘成果异常 >> 预核、实核 >> 业务ID：{record.ID} >> 测绘单位：{record.SurveyCompanyName}。异常信息：{e.GetStackTraces()} ");
                return new ApiResult() { StateCode = 0, Message = "提交失败，错误信息：" + e.Message + "" };
            }

        }
        /// <summary>
/// 提交征地拆迁数据交汇MDB测绘成果
/// </summary>
/// <param name="id">业务ID</param>
/// <returns></returns>
[HttpPost, Route("PostLandSurveyMdbResult")]
public async Task<ApiResult> PostLandSurveyMdbResult(string id) {
    if (string.IsNullOrWhiteSpace(id)) {
        return new ApiResult { StateCode = 0, Message = "请输入正确的信息" };
    }

    var record = service.GetById<BusinessBaseInfo>(id);
    if (record == null) {
        return new ApiResult { StateCode = 0, Message = "无法找到业务信息" };
    }

    if (record.BusinessClass != nameof(LandSurveyProjectFlow)) {
        return new ApiResult { StateCode = 0, Message = "业务类型不正确" };
    }

    try {
        HttpFileCollection files = System.Web.HttpContext.Current.Request.Files;
        //判断是否有文件上传
        if (files.Count == 0) {
            return new ApiResult { StateCode = 0, Message = "请选择要上传的文件" };
        }
       
        // 验证文件扩展名
        string fileName = files[0].FileName;
        if (string.IsNullOrEmpty(fileName) || !fileName.ToLower().EndsWith(".mdb")) {
            return new ApiResult { StateCode = 0, Message = "请上传.mdb格式的文件" };
        }

        Stream stream = files[0].InputStream;
        byte[] bytes = new byte[stream.Length];
        stream.Read(bytes, 0, bytes.Length);
        stream.Flush();
        stream.Close();
        stream.Dispose();

        Request.WriteSDCLog("业务办理", $"开始上传征地拆迁MDB测绘成果 >> 业务ID：{record.ID} >> 测绘单位：{record.SurveyCompanyName} >> 原始文件名：{fileName} >> 处理后文件名：{safeFileName}");

        #region 清空原来的数据，避免使用的是上一次的检查通过的结果

        //更新检查信息至contentinfo
        LandSurvey_ContentInfo content = service.GetById<LandSurvey_ContentInfo>(record.ID);
        content.DataCheckID = null;
        content.DataCheckState = 0;
        //如果是重新上传成果，则需要先移除原来的成果
        content.ProjectResultInfo = null;
        content.SurveyMasterName = null;
        content.SurveyMasterNo = null;
        content.SurveyMasterSureTime = null;
        content.RejectMessage = null;
        service.UpdateOne(content);

        //成果附件逻辑删除
        var attachments = service.GetList<AttachmentInfo>($"BusinessID='{record.ID}' AND AttachmentType = '项目成果附件' AND AttachmentExt='.txt'");
        if (attachments.Any()) {
            foreach (var item in attachments) {
                if (item.StateCode != 1) {
                    item.StateCode = 1;
                    service.UpdateOne(item);
                }
            }
        }

        #endregion

        string taskId = "";

        // 调用新的mdb检查接口上传文件
        string uploadUrl = $"{MdbCheckApiUrl}/mdb/upload";

        // 确保文件名只包含文件名部分，不包含路径
        string cleanFileName = Path.GetFileName(fileName);

        // 处理文件名编码问题：检查是否包含非ASCII字符
        string safeFileName;
        bool hasNonAscii = cleanFileName.Any(c => c > 127);

        if (!hasNonAscii) {
            // 纯英文文件名，直接使用
            safeFileName = cleanFileName;
        } else {
            // 包含中文字符，进行URL编码处理
            safeFileName = HttpUtility.UrlEncode(cleanFileName, Encoding.UTF8);
        }

        var dataContent = new ByteArrayContent(bytes);
        dataContent.Headers.ContentDisposition = new ContentDispositionHeaderValue("form-data") {
            Name = "file",
            FileName = safeFileName,
        };

        using (var content2 = new MultipartFormDataContent()) {
            string boundary = string.Format("--{0}", DateTime.Now.Ticks.ToString("x"));
            content2.Headers.Add("ContentType", $"multipart/form-data, boundary={boundary}");

            content2.Add(dataContent);

            var httpClient = HttpClientFactory.Create();
            var responseMessage = await httpClient.PostAsync(uploadUrl, content2);
            if (responseMessage.IsSuccessStatusCode) {
                var responseString = await responseMessage.Content.ReadAsStringAsync();

                var jObj = JsonConvert.DeserializeObject<JObject>(responseString);
                taskId = jObj["task_id"]?.ToString();

                if (string.IsNullOrWhiteSpace(taskId)) {
                    //接口有异常
                    return new ApiResult() { StateCode = 0, Message = $"提交失败，mdb检查接口异常，接口返回消息为：{jObj["message"]?.ToString()}" };
                }

                // 调用分析接口
                string checkUrl = $"{MdbCheckApiUrl}/mdb/check/{taskId}";
                var checkResponse = await httpClient.PostAsync(checkUrl, new StringContent(""));
                if (!checkResponse.IsSuccessStatusCode) {
                    return new ApiResult() { StateCode = 0, Message = "启动mdb检查失败" };
                }
            }
            else {
                var errorContent = await responseMessage.Content.ReadAsStringAsync();
                Request.WriteSDCLog("业务办理", $"征地拆迁MDB上传失败 >> 业务ID：{record.ID} >> 状态码：{(int)responseMessage.StatusCode} >> 原始文件名：{cleanFileName} >> 安全文件名：{safeFileName} >> 文件大小：{bytes.Length} bytes >> 错误详情：{errorContent}");
                return new ApiResult() {
                    StateCode = 0,
                    Message = $"上传接口返回状态失败：{(int)responseMessage.StatusCode},详细信息：{errorContent}。请检查文件是否为有效的.mdb格式文件。"
                };
            }
        }
        //更新检查信息至contentinfo
        content.DataCheckID = taskId;
        content.DataCheckState = 0;
        service.UpdateOne(content);

        //启动异步作业进行检测
        BackgroundJob.Schedule(() => SurveyResultJob.LoadMdbCheckState(taskId, record.ID, null), TimeSpan.FromMinutes(1));

        Request.WriteSDCLog("业务办理", $"提交征地拆迁MDB测绘成果成功 >> 业务ID：{record.ID} >> 测绘单位：{record.SurveyCompanyName} >> 原始文件名：{cleanFileName} >> 安全文件名：{safeFileName} >> 任务ID：{taskId}");
        return new ApiResult() { StateCode = 1, Data = taskId, Message = "提交成功" };
    }
    catch (Exception e) {
        string fileName = files?.Count > 0 ? files[0]?.FileName : "未知";
        Request.WriteSDCLog("业务办理", $"提交征地拆迁MDB测绘成果异常 >> 业务ID：{record.ID} >> 测绘单位：{record.SurveyCompanyName} >> 文件名：{fileName} >> 异常信息：{e.GetStackTraces()}");
        return new ApiResult() { StateCode = 0, Message = "提交失败，错误信息：" + e.Message + "。请检查文件是否为有效的.mdb格式文件，文件大小是否合适。" };
    }
}

        /// <summary>
        /// 上传项目范围
        /// </summary>
        /// <param name="id"></param>
        /// <param name="applyData"></param>
        /// <returns></returns>
        [HttpPost, Route("PostProjectScopeResult")]
        public ApiResult PostProjectScopeResult(string id, string applyData) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "请输入正确的信息" };
            }
            var record = service.GetById<BusinessBaseInfo>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到业务信息" };
            }
            try {
                HttpFileCollection files = System.Web.HttpContext.Current.Request.Files;
                //判断是否有文件上传
                if (files.Count == 0) {
                    return new ApiResult { StateCode = 0, Message = "提交失败，请选择要上传的文件" };
                }
                //防止前端Bug  后端增加拦截
                var list = service.GetList<AttachmentInfo>($"BusinessID='{record.ID}' AND AttachmentType = '基础数据' and StateCode=0");
                if (list.Count > 0) {
                    return new ApiResult { StateCode = 0, Message = "该业务已经完成下载，不允许再次上传项目范围坐标" };
                }
                string attachType = "项目范围坐标";
                //项目范围坐标文件逻辑删除
                var attachments = service.GetList<AttachmentInfo>($"BusinessID='{record.ID}' AND AttachmentCategories = '{attachType}'");
                if (attachments.Any()) {
                    foreach (var item in attachments) {
                        if (item.StateCode != 1) {
                            item.StateCode = 1;
                            service.UpdateOne(item);
                        }
                    }
                }
                string newId = Guid.NewGuid().ToString("N");
                //先保存 否则经过StreamReader处理后 InputStream会变空
                SaveData(id, newId, attachType, files[0]);
                StreamReader sr2 = new StreamReader(files[0].InputStream, Encoding.UTF8);
                string result2 = sr2.ReadToEnd().Trim();
                result2 = result2.Replace("\r\n", ",").Trim();
                result2 = result2.Replace("\n", ",").Trim();
                sr2.Close();
                sr2.Dispose();
                var servicesUrl = ExternalApiConfig.EPSSDCServices + "/CheckProjectArea";
                string par = $"ID={id}&strYWMC={HttpUtility.UrlEncode(record.BusinessType)}&strCOORDINATE={LongUrlEncode(result2)}";
                string responseString = "";
                byte[] bytes = Encoding.UTF8.GetBytes(par);
                HttpWebRequest request = WebRequest.Create(servicesUrl) as HttpWebRequest;
                request.Method = "POST";
                request.ContentType = "application/x-www-form-urlencoded";
                request.ContentLength = (long)bytes.Length;
                using (Stream requestStream = request.GetRequestStream()) {
                    requestStream.Write(bytes, 0, bytes.Length);
                    requestStream.Flush();
                    requestStream.Close();
                }
                WebResponse rs = request.GetResponse();
                using (HttpWebResponse response = request.GetResponse() as HttpWebResponse) {
                    StreamReader reader = new StreamReader(response.GetResponseStream());
                    responseString = reader.ReadToEnd();
                    reader.Close();
                }
                System.Xml.XmlDocument xml = new System.Xml.XmlDocument();
                xml.LoadXml(responseString);
                JObject result = (JObject)JsonConvert.DeserializeObject(xml.InnerText);
                string ProjectAreaStatus = result["PROJECTAREASTATUS"]?.Value<string>();
                string ProjectAreaError = result["PROJECTAREAERROR"]?.Value<string>();
                string resultStatus = result["status"]?.Value<string>();
                if (resultStatus != "success") {
                    return new ApiResult() { StateCode = 0, Message = $"提交失败，数据检查接口异常，接口返回消息为：{ProjectAreaError}" };
                }
                int checkState = 0;
                if (record.BusinessClass == "BaseSurveyDataDownloadFlow") {
                    if (ProjectAreaStatus == "2") {
                        checkState = 1;
                    }
                    else {
                        checkState = 2;
                    }
                    BaseGISData_ContentInfo content = service.GetById<BaseGISData_ContentInfo>(record.ID);
                    content.DataCheckID = id;//暂存为业务id
                    content.DataCheckState = checkState;
                    content.FailedReason = checkState == 1 ? null : ProjectAreaError;
                    service.UpdateOne(content);
                }
                if (record.BusinessClass == "BlueLineSurveyFlow") {
                    //暂时不考虑该业务
                    BlueLineSurveyContentInfo content = service.GetById<BlueLineSurveyContentInfo>(record.ID);
                    content.DataCheckID = id;//暂存为业务id
                    content.DataCheckState = 0;
                    content.FailedReason = null;
                    service.UpdateOne(content);
                }
                if (record.BusinessClass == "MarkPointSurveyFlow") {
                    if (ProjectAreaStatus == "2") {
                        checkState = 1;
                    }
                    else if (ProjectAreaStatus == "0") {
                        checkState = 2;
                    }
                    else {
                        checkState = 3;
                    }
                    //更新检查信息至contentinfo
                    MarkPointSurveyContentInfo content = service.GetById<MarkPointSurveyContentInfo>(record.ID);
                    content.DataCheckID = id;//暂存为业务id
                    content.DataCheckState = checkState;
                    content.FailedReason = checkState == 2 ? ProjectAreaError : null;
                    content.ApplyData = applyData;
                    service.UpdateOne(content);
                }
                //拨地定桩审核通过  直接启动下载
                if (record.BusinessClass == "MarkPointSurveyFlow" && ProjectAreaStatus == "2") {
                    PostEpsDownLoadResult(id);
                }
                //只有审核不通过  移除附件    
                if (ProjectAreaStatus == "0") {
                    string Year = DateTime.Now.Year.ToString();
                    string Month = DateTime.Now.Month.ToString();
                    string Day = DateTime.Now.Day.ToString();
                    string Path = RootPath + "\\" + Year + "\\" + Month + "\\" + Day + "\\" + id + "\\" + newId;
                    File.Delete(Path);
                    AttachmentInfo attachmentInfo = service.GetById<AttachmentInfo>(newId);
                    service.DeleteOne(attachmentInfo);
                }

                Request.WriteSDCLog("业务办理", $"上传项目范围 >> 业务ID：{record.ID} >> 测绘单位：{record.SurveyCompanyName} ");

                if (record.BusinessClass == "BaseSurveyDataDownloadFlow") {
                    BaseSurveyDataDownloadProject project = BaseSurveyDataDownloadProject.GetByBusinessID(id);
                    return new ApiResult() { StateCode = 1, Message = "提交成功", Data = project };
                }
                else {
                    MarkPointSurveyProject project = MarkPointSurveyProject.GetByBusinessID(id);
                    return new ApiResult() { StateCode = 1, Message = "提交成功", Data = project };
                }


            }
            catch (Exception e) {
                return new ApiResult() { StateCode = 0, Message = "提交失败，错误信息：" + e.Message + "", Data = e.InnerException?.ToString() };
            }

        }
        /// <summary>
        /// 保存文件
        /// </summary>
        /// <param name="id"></param>
        /// <param name="newId"></param>
        /// <param name="attachType"></param>
        /// <param name="file"></param>
        public void SaveData(string id, string newId, string attachType, HttpPostedFile file) {
            var record = service.GetById<BusinessBaseInfo>(id);
            #region 保存上传的文件
            string Year = DateTime.Now.Year.ToString();
            string Month = DateTime.Now.Month.ToString();
            string Day = DateTime.Now.Day.ToString();
            //文件存储路径
            string SavePath = Year + "\\" + Month + "\\" + Day + "\\" + id;
            //路径不存在时，自动创建
            if (!Directory.Exists(RootPath + "\\" + SavePath)) {
                Directory.CreateDirectory(RootPath + "\\" + SavePath);
            }

            SavePath = SavePath + "\\" + newId;
            //文件原始后缀名
            string OldFileEextension = Path.GetExtension(file.FileName);
            //文件名称
            string Fname = Path.GetFileNameWithoutExtension(file.FileName);
            //保存文件
            file.SaveAs(RootPath + "\\" + SavePath);
            AttachmentInfo Attachment = new AttachmentInfo {
                ID = newId,
                BusinessID = id,
                BusinessType = record.BusinessType,
                AttachmentType = "申请材料附件",
                AttachmentCategories = attachType,
                //LiftTime = DateTime.Now,
                //ExpirationTime = DateTime.Now.AddYears(10),//获取数据时不支持时间字段为空，默认十年后才过期
                StateCode = 0,
                AttachmentName = attachType,
                AttachmentExt = OldFileEextension,
                UploadTime = DateTime.Now,
                AttachmentPath = SavePath,
                AttachmentLength = file.ContentLength,
            };
            service.InsertOne(Attachment);
            #endregion
        }

        /// <summary>
        /// 启动EPS进行数据切图
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("PostEpsDownLoadResult")]
        public ApiResult PostEpsDownLoadResult(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "请输入正确的信息" };
            }
            var record = service.GetById<BusinessBaseInfo>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到业务信息" };
            }
            try {
                string attachType = "项目范围坐标";
                List<AttachmentInfo> list = service.GetList<AttachmentInfo>($"BusinessID='{record.ID}' AND AttachmentCategories = '{attachType}' and StateCode=0");
                //附件存储根路径
                string RootPath = WebConfigurationManager.AppSettings["RootPath"];
                //获取上传项目范围坐标
                FileStream file = new FileStream(RootPath + "\\" + list[0].AttachmentPath, FileMode.Open);
                StreamReader sr2 = new StreamReader(file);
                string result2 = sr2.ReadToEnd().Trim();
                result2 = result2.Replace("\r\n", ",").Trim();
                result2 = result2.Replace("\n", ",").Trim();
                sr2.Close();
                var servicesUrl = ExternalApiConfig.EPSSDCServices + "/DownloadBaseData";

                string strDownloadLayer = "地形数据,路网数据";
                if (record.BusinessClass == "BaseSurveyDataDownloadFlow") {
                    BaseGISData_ContentInfo baseGisData = service.GetById<BaseGISData_ContentInfo>(record.ID);
                    JArray lb = (JArray)JsonConvert.DeserializeObject(baseGisData.ApplyData);
                    strDownloadLayer = "";
                    for (int i = 0; i < lb.Count; i++) {
                        if (lb[i].ToString().Contains("地形")) {
                            strDownloadLayer += "地形数据";
                        }
                        if (lb[i].ToString().Contains("路网")) {
                            strDownloadLayer += "路网数据";
                        }
                        if (lb[i].ToString().Contains("规划")) {
                            strDownloadLayer += "控制性详细规划数据";
                        }
                        if (i < lb.Count - 1) {
                            strDownloadLayer += ",";
                        }
                    }
                }
                if (record.BusinessClass == "MarkPointSurveyFlow") {
                    MarkPointSurveyContentInfo markPointData = service.GetById<MarkPointSurveyContentInfo>(record.ID);
                    JArray lb = (JArray)JsonConvert.DeserializeObject(markPointData.ApplyData);
                    strDownloadLayer = "";
                    for (int i = 0; i < lb.Count; i++) {
                        if (lb[i].ToString().Contains("地形")) {
                            strDownloadLayer += "地形数据";
                        }
                        if (lb[i].ToString().Contains("路网")) {
                            strDownloadLayer += "路网数据";
                        }
                        if (i < lb.Count - 1) {
                            strDownloadLayer += ",";
                        }
                    }
                }
                string par = $"ID={id}&strYWMC={HttpUtility.UrlEncode(record.BusinessType)}&strDownloadLayer={HttpUtility.UrlEncode(strDownloadLayer)}&strCOORDINATE={LongUrlEncode(result2)}";
                string responseString = "";
                byte[] bytes = Encoding.UTF8.GetBytes(par);
                HttpWebRequest request = WebRequest.Create(servicesUrl) as HttpWebRequest;
                request.Method = "POST";
                request.ContentType = "application/x-www-form-urlencoded";
                request.ContentLength = (long)bytes.Length;
                using (Stream requestStream = request.GetRequestStream()) {
                    requestStream.Write(bytes, 0, bytes.Length);
                    requestStream.Flush();
                    requestStream.Close();
                }
                WebResponse rs = request.GetResponse();
                using (HttpWebResponse response = request.GetResponse() as HttpWebResponse) {
                    StreamReader reader = new StreamReader(response.GetResponseStream());
                    responseString = reader.ReadToEnd();
                    reader.Close();
                }
                System.Xml.XmlDocument xml = new System.Xml.XmlDocument();
                xml.LoadXml(responseString);
                JObject result = (JObject)JsonConvert.DeserializeObject(xml.InnerText);
                string fileId = result["FILEID"]?.Value<string>();
                if (string.IsNullOrWhiteSpace(fileId)) {
                    //EPS接口有异常，仅返回status信息
                    return new ApiResult() { StateCode = 0, Message = $"提交失败，数据检查接口异常，接口返回消息为：{result["status"].Value<string>()}" };
                }
                if (record.BusinessClass == "BaseSurveyDataDownloadFlow") {
                    //更新检查信息至contentinfo
                    BaseGISData_ContentInfo content = service.GetById<BaseGISData_ContentInfo>(record.ID);
                    content.DownLoadID = fileId;
                    content.DownLoadState = 0;
                    service.UpdateOne(content);
                }
                if (record.BusinessClass == "BlueLineSurveyFlow") {
                    //更新检查信息至contentinfo
                    BlueLineSurveyContentInfo content = service.GetById<BlueLineSurveyContentInfo>(record.ID);
                    content.DownLoadID = fileId;
                    content.DownLoadState = 0;
                    service.UpdateOne(content);
                }
                if (record.BusinessClass == "MarkPointSurveyFlow") {
                    MarkPointSurveyContentInfo content = service.GetById<MarkPointSurveyContentInfo>(record.ID);
                    content.DownLoadID = fileId;
                    content.DownLoadState = 0;
                    service.UpdateOne(content);
                }
                //启动异步作业进行检测
                BackgroundJob.Schedule(() => SurveyResultJob.LoadEPSDownLoadState(fileId, record.ID, null), TimeSpan.FromMinutes(1));

                return new ApiResult() { StateCode = 1, Data = fileId, Message = "提交成功" };
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "提交失败，错误信息：" + e.Message + "" };
            }
        }
        /// <summary>
        /// 获取成果检查状态
        /// </summary>
        /// <param name="checkid"></param>
        /// <param name="businessClass"></param>
        /// <returns></returns>
        [HttpGet, Route("GetSurveyResultCheckState")]
        public ApiResult GetSurveyResultCheckState(string checkid, string businessClass) {
            /* todo 此接口为前端调用，则只需要检测ContentInfo的检测状态即可*/
            if (checkid == null) {
                return new ApiResult() { StateCode = 0, Message = "检查ID不能为空" };
            }
            try {
                if (businessClass == "RealEstatePreSurveyFlow" 
                    || businessClass == "RealEstatePreCheckSurveyFlow"
                    || businessClass == "RealEstatePreSurveyResultChangeFlow"
                    || businessClass == "RealEstatePreCheckSurveyAutoFlow"
                    ) {
                    List<PreSurvey_ContentInfo> projectList = service.GetList<PreSurvey_ContentInfo>("DataCheckID='" + checkid + "'");
                    if (projectList.Count == 0) {
                        return new ApiResult() { StateCode = 0, Message = "检查ID无效" };
                    }
                    return new ApiResult() { StateCode = 1, Data = projectList[0].DataCheckState, Message = "获取成功" };
                }
                if (businessClass == "RealEstateActualSurveyFlow" 
                    || businessClass == "RealEstateActualResultChangeFlow"
                    || businessClass == "RealEstateOverallActualSurveyFlow"
                ) {
                    List<EstateActualSurveyContentInfo> projectList = service.GetList<EstateActualSurveyContentInfo>("DataCheckID='" + checkid + "'");
                    if (projectList.Count == 0) {
                        return new ApiResult() { StateCode = 0, Message = "检查ID无效" };
                    }
                    return new ApiResult() { StateCode = 1, Data = projectList[0].DataCheckState, Message = "获取成功" };
                }
                if (businessClass == "CouncilPlanCheckFlow") {
                    List<CouncilPlanCheckContentInfo> projectList = service.GetList<CouncilPlanCheckContentInfo>("DataCheckID='" + checkid + "'");
                    if (projectList.Count == 0) {
                        return new ApiResult() { StateCode = 0, Message = "检查ID无效" };
                    }
                    return new ApiResult() { StateCode = 1, Data = projectList[0].DataCheckState, Message = "获取成功" };
                }
                return new ApiResult { StateCode = 0, Message = $"业务逻辑类{businessClass}尚未支持" };
            }
            catch (Exception e) {
                return new ApiResult() { StateCode = 0, Message = "获取失败，错误信息：" + e.Message + "" };
            }
        }
        /// <summary>
        /// 获取项目范围检查状态
        /// </summary>
        /// <param name="checkid"></param>
        /// <param name="businessClass"></param>
        /// <returns></returns>
        [HttpGet, Route("GetProjectScopeCheckState")]
        public ApiResult GetProjectScopeCheckState(string checkid, string businessClass) {
            /* todo 此接口为前端调用，则只需要检测ContentInfo的检测状态即可*/
            if (checkid == null) {
                return new ApiResult() { StateCode = 0, Message = "检查ID不能为空" };
            }
            try {
                if (businessClass == "BaseSurveyDataDownloadFlow") {
                    List<BaseGISData_ContentInfo> projectList = service.GetList<BaseGISData_ContentInfo>("DataCheckID='" + checkid + "'");
                    if (projectList.Count == 0) {
                        return new ApiResult() { StateCode = 0, Message = "检查ID无效" };
                    }
                    dynamic resultInfo = new {
                        DataCheckState = projectList[0].DataCheckState,
                        FailedReason = projectList[0].FailedReason
                    };
                    return new ApiResult() { StateCode = 1, Data = resultInfo, Message = "获取成功" };
                }
                if (businessClass == "BlueLineSurveyFlow") {
                    List<BlueLineSurveyContentInfo> projectList = service.GetList<BlueLineSurveyContentInfo>("DataCheckID='" + checkid + "'");
                    if (projectList.Count == 0) {
                        return new ApiResult() { StateCode = 0, Message = "检查ID无效" };
                    }
                    dynamic resultInfo = new {
                        DataCheckState = projectList[0].DataCheckState,
                        FailedReason = projectList[0].FailedReason
                    };
                    return new ApiResult() { StateCode = 1, Data = resultInfo, Message = "获取成功" };
                }
                if (businessClass == "MarkPointSurveyFlow") {
                    List<MarkPointSurveyContentInfo> projectList = service.GetList<MarkPointSurveyContentInfo>("DataCheckID='" + checkid + "'");
                    if (projectList.Count == 0) {
                        return new ApiResult() { StateCode = 0, Message = "检查ID无效" };
                    }
                    dynamic resultInfo = new {
                        DataCheckState = projectList[0].DataCheckState,
                        FailedReason = projectList[0].FailedReason
                    };
                    return new ApiResult() { StateCode = 1, Data = resultInfo, Message = "获取成功" };
                }
                return new ApiResult() { StateCode = 0, Message = "获取失败，业务类型无效" };





            }
            catch (Exception e) {
                return new ApiResult() { StateCode = 0, Message = "获取失败，错误信息：" + e.Message + "" };
            }
        }
        /// <summary>
        /// 获取EPS下载状态
        /// </summary>
        /// <param name="downloadid"></param>
        /// <param name="businessClass"></param>
        /// <returns></returns>
        [HttpGet, Route("GetEPSDownLoadState")]
        public ApiResult GetEPSDownLoadState(string downloadid, string businessClass) {
            /* todo 此接口为前端调用，则只需要检测ContentInfo的下载状态即可*/
            if (downloadid == null) {
                return new ApiResult() { StateCode = 0, Message = "下载ID不能为空" };
            }
            try {
                if (businessClass == "BaseSurveyDataDownloadFlow") {
                    List<BaseGISData_ContentInfo> projectList = service.GetList<BaseGISData_ContentInfo>("DownLoadID='" + downloadid + "'");
                    if (projectList.Count == 0) {
                        return new ApiResult() { StateCode = 0, Message = "下载ID无效" };
                    }
                    return new ApiResult() { StateCode = 1, Data = projectList[0].DownLoadState, Message = "获取成功" };
                }
                if (businessClass == "BlueLineSurveyFlow") {
                    List<BlueLineSurveyContentInfo> projectList = service.GetList<BlueLineSurveyContentInfo>("DownLoadID='" + downloadid + "'");
                    if (projectList.Count == 0) {
                        return new ApiResult() { StateCode = 0, Message = "下载ID无效" };
                    }
                    return new ApiResult() { StateCode = 1, Data = projectList[0].DownLoadState, Message = "获取成功" };
                }
                if (businessClass == "MarkPointSurveyFlow") {
                    List<MarkPointSurveyContentInfo> projectList = service.GetList<MarkPointSurveyContentInfo>("DownLoadID='" + downloadid + "'");
                    if (projectList.Count == 0) {
                        return new ApiResult() { StateCode = 0, Message = "下载ID无效" };
                    }
                    return new ApiResult() { StateCode = 1, Data = projectList[0].DownLoadState, Message = "获取成功" };
                }
                return new ApiResult() { StateCode = 0, Message = "获取失败，业务类型无效" };
            }
            catch (Exception e) {
                return new ApiResult() { StateCode = 0, Message = "获取失败，错误信息：" + e.Message + "" };
            }
        }
        /// <summary>
        /// 获取汇交成果检查状态
        /// </summary>
        /// <param name="checkid"></param>
        /// <param name="businessClass"></param>
        /// <returns></returns>
        [HttpGet, Route("GetAchievementCheckState")]
        public ApiResult GetAchievementCheckState(string checkid, string businessClass) {
            /* todo 此接口为前端调用，则只需要检测ContentInfo的检测状态即可*/
            if (checkid == null) {
                return new ApiResult() { StateCode = 0, Message = "检查ID不能为空" };
            }
            if (businessClass == null) {
                return new ApiResult() { StateCode = 0, Message = "业务逻辑类不能为空" };
            }
            try {
                if (businessClass == "BlueLineSurveyFlow") {
                    List<BlueLineSurveyContentInfo> projectList = service.GetList<BlueLineSurveyContentInfo>("AchievementCheckID='" + checkid + "'");
                    if (projectList.Count == 0) {
                        return new ApiResult() { StateCode = 0, Message = "检查ID无效" };
                    }
                    return new ApiResult() { StateCode = 1, Data = projectList[0].AchievementCheckState, Message = "获取成功" };
                }
                if (businessClass == "MarkPointSurveyFlow") {
                    List<MarkPointSurveyContentInfo> projectList = service.GetList<MarkPointSurveyContentInfo>("AchievementCheckID='" + checkid + "'");
                    if (projectList.Count == 0) {
                        return new ApiResult() { StateCode = 0, Message = "检查ID无效" };
                    }
                    return new ApiResult() { StateCode = 1, Data = projectList[0].AchievementCheckState, Message = "获取成功" };
                }
                return new ApiResult() { StateCode = 0, Message = "获取失败，业务类型无效" };
            }
            catch (Exception e) {
                return new ApiResult() { StateCode = 0, Message = "获取失败，错误信息：" + e.Message + "" };
            }
        }
        /// <summary>
        /// 注册测绘师刷脸确认测绘成果
        /// </summary>
        /// <param name="id"></param>
        /// <param name="actionId"></param>
        /// <param name="certifyId">刷脸ID</param>
        /// <param name="certifyType">刷脸类型，1公安认证，2支付宝认证</param>
        /// <returns></returns>
        [HttpPost, Route("ConfirmSurveyResult")]
        public ApiResult ConfirmSurveyResult(string id, string actionId, string certifyId = null, int certifyType = 1) {
            //todo 根据业务ID获取测绘单位，然后判断本用户是否为此测绘单位的注册测绘师，
            //todo 成果确认时记录注册测绘师的姓名，身份证号，确认时间（信息存放在ContentInfo表），然后流程自动提交下一步
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "请输入正确的信息" };
            }
            var record = service.GetById<BusinessBaseInfo>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到业务信息" };
            }
            try {
                var personNo = UserInfo.PersonNo;
                var personName = UserInfo.PersonName;
                UserRole roleName;
                CompanyBaseInfo company;
                //根据身份证号获取绑定单位的信息以及员工角色
                GetSurveyCompanyInfoAndRoleByPersonNo(personNo, out roleName, out company);
                if (roleName != UserRole.SurveyMaster) {
                    return new ApiResult { StateCode = 0, Message = "当前用户不属于注册测绘师角色" };
                }
                if (company.CreditCode != record.SurveyCompanyNo) {
                    return new ApiResult { StateCode = 0, Message = "当前用户不属于本测绘单位注册测绘师角色" };
                }

                if (record.StateCode == 0) {
                    //先签收业务
                    AcceptBusiness(id);
                }

                var userId = UserInfo.UserId;
                string cannotFlowPostMessage;
                if (!CanFlowPost(record, actionId, userId, out cannotFlowPostMessage)) {
                    return new ApiResult { StateCode = 0, Message = $"操作失败，{cannotFlowPostMessage}" };
                }
                HttpRequestBase req = ((HttpContextBase)Request.Properties["MS_HttpContext"]).Request;
                var reqInfoHelper = new HttpRequestInfoHelper(req);
                LogService.WriteLogs(Request, "业务管理", $"确认测绘成果 >> 业务ID：{record.ID} >> 业务类型：{record.BusinessType}", reqInfoHelper);
                if (record.BusinessClass == "MeasurePutLineFlow") {
                    //更新注册测绘师信息至contentinfo
                    PutLine_ContentInfo content = service.GetById<PutLine_ContentInfo>(record.ID);
                    content.SurveyMasterName = personName;
                    content.SurveyMasterNo = personNo;
                    content.SurveyMasterSureTime = DateTime.Now;
                    content.DataCheckState = 1;
                    service.UpdateOne(content);
                    //提交至下一步
                    FlowService<MeasurePutLineFlow> flow = new FlowService<MeasurePutLineFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    MeasurePutLineProject project = MeasurePutLineProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
                }
                if (record.BusinessClass == "RealEstatePreSurveyFlow") {
                    //更新注册测绘师信息至contentinfo
                    PreSurvey_ContentInfo content = service.GetById<PreSurvey_ContentInfo>(record.ID);
                    content.SurveyMasterName = personName;
                    content.SurveyMasterNo = personNo;
                    content.SurveyMasterSureTime = DateTime.Now;
                    service.UpdateOne(content);
                    //提交至下一步
                    FlowService<RealEstatePreSurveyFlow> flow = new FlowService<RealEstatePreSurveyFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    RealEstatePreSurveyProject project = RealEstatePreSurveyProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
                }
                if (record.BusinessClass == "RealEstatePreCheckSurveyFlow") {
                    //更新注册测绘师信息至contentinfo
                    {
                        PreSurvey_ContentInfo content = service.GetById<PreSurvey_ContentInfo>(record.ID);
                        content.SurveyMasterName = personName;
                        content.SurveyMasterNo = personNo;
                        content.SurveyMasterSureTime = DateTime.Now;
                        service.UpdateOne(content);
                    }
                    {
                        PutLine_ContentInfo content = service.GetById<PutLine_ContentInfo>(record.ID);
                        content.SurveyMasterName = personName;
                        content.SurveyMasterNo = personNo;
                        content.SurveyMasterSureTime = DateTime.Now;
                        content.DataCheckState = 1;
                        service.UpdateOne(content);
                    }
                    //提交至下一步
                    FlowService<RealEstatePreCheckSurveyFlow> flow = new FlowService<RealEstatePreCheckSurveyFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    RealEstatePreCheckSurveyProject project = RealEstatePreCheckSurveyProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
                }
                if (record.BusinessClass == "BlueLineSurveyFlow") {
                    //更新注册测绘师信息至contentinfo
                    BlueLineSurveyContentInfo content = service.GetById<BlueLineSurveyContentInfo>(record.ID);
                    content.SurveyMasterName = personName;
                    content.SurveyMasterNo = personNo;
                    content.SurveyMasterSureTime = DateTime.Now;
                    service.UpdateOne(content);
                    //提交至下一步
                    FlowService<BlueLineSurveyFlow> flow = new FlowService<BlueLineSurveyFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    BlueLineSurveyProject project = BlueLineSurveyProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
                }
                if (record.BusinessClass == "MarkPointSurveyFlow") {
                    //更新注册测绘师信息至contentinfo
                    MarkPointSurveyContentInfo content = service.GetById<MarkPointSurveyContentInfo>(record.ID);
                    content.SurveyMasterName = personName;
                    content.SurveyMasterNo = personNo;
                    content.SurveyMasterSureTime = DateTime.Now;
                    service.UpdateOne(content);
                    //提交至下一步
                    FlowService<MarkPointSurveyFlow> flow = new FlowService<MarkPointSurveyFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    MarkPointSurveyProject project = MarkPointSurveyProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
                }
                if (record.BusinessClass == "PlanCheckSurveyFlow") {
                    //更新注册测绘师信息至contentinfo
                    //PlanCheckSurveyContentInfo content = service.GetById<PlanCheckSurveyContentInfo>(record.ID);
                    //content.SurveyMasterName = personName;
                    //content.SurveyMasterNo = personNo;
                    //content.SurveyMasterSureTime = DateTime.Now;
                    //content.DataCheckState = 1;
                    //service.UpdateOne(content);
                    ////提交至下一步
                    //FlowService<PlanCheckSurveyFlow> flow = new FlowService<PlanCheckSurveyFlow>(service);
                    //flow.FlowPostToNext(record.ID);
                    //PlanCheckSurveyProject project = PlanCheckSurveyProject.GetByBusinessID(id);
                    //return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };暂不支持该业务
                    return new ApiResult() { StateCode = 0, Message = "确认失败，错误信息：暂不支持该业务" };
                }
                if (record.BusinessClass == "RealEstateActualSurveyFlow") {
                    //更新注册测绘师信息至contentinfo
                    EstateActualSurveyContentInfo content = service.GetById<EstateActualSurveyContentInfo>(record.ID);
                    content.SurveyMasterName = personName;
                    content.SurveyMasterNo = personNo;
                    content.SurveyMasterSureTime = DateTime.Now;
                    service.UpdateOne(content);
                    //提交至下一步
                    FlowService<RealEstateActualSurveyFlow> flow = new FlowService<RealEstateActualSurveyFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    RealEstateActualSurveyProject project = RealEstateActualSurveyProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
                }
                if (record.BusinessClass == "RealEstatePreSurveyBuildingTableChangeFlow") {
                    //更新注册测绘师信息至contentinfo
                    EstateActualSurveyContentInfo content = service.GetById<EstateActualSurveyContentInfo>(record.ID);
                    content.SurveyMasterName = personName;
                    content.SurveyMasterNo = personNo;
                    content.SurveyMasterSureTime = DateTime.Now;
                    service.UpdateOne(content);
                    //提交至下一步
                    FlowService<RealEstatePreSurveyBuildingTableChangeFlow> flow = new FlowService<RealEstatePreSurveyBuildingTableChangeFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    RealEstatePreSurveyBuildingTableChangeProject project = RealEstatePreSurveyBuildingTableChangeProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
                }
                if (record.BusinessClass == "RealEstatePreSurveyResultChangeFlow") {
                    //更新注册测绘师信息至contentinfo
                    PreSurvey_ContentInfo content = service.GetById<PreSurvey_ContentInfo>(record.ID);
                    content.SurveyMasterName = personName;
                    content.SurveyMasterNo = personNo;
                    content.SurveyMasterSureTime = DateTime.Now;
                    service.UpdateOne(content);
                    //提交至下一步
                    FlowService<RealEstatePreSurveyResultChangeFlow> flow = new FlowService<RealEstatePreSurveyResultChangeFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    RealEstatePreSurveyResultChangeProject project = RealEstatePreSurveyResultChangeProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
                }
                if (record.BusinessClass == "RealEstateActualBuildingTableChangeFlow") {
                    //更新注册测绘师信息至contentinfo
                    EstateActualSurveyContentInfo content = service.GetById<EstateActualSurveyContentInfo>(record.ID);
                    content.SurveyMasterName = personName;
                    content.SurveyMasterNo = personNo;
                    content.SurveyMasterSureTime = DateTime.Now;
                    service.UpdateOne(content);
                    //提交至下一步
                    FlowService<RealEstateActualBuildingTableChangeFlow> flow = new FlowService<RealEstateActualBuildingTableChangeFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    RealEstateActualBuildingTableChangeProject project = RealEstateActualBuildingTableChangeProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
                }
                if (record.BusinessClass == "RealEstateActualResultChangeFlow") {
                    //更新注册测绘师信息至contentinfo
                    EstateActualSurveyContentInfo content = service.GetById<EstateActualSurveyContentInfo>(record.ID);
                    content.SurveyMasterName = personName;
                    content.SurveyMasterNo = personNo;
                    content.SurveyMasterSureTime = DateTime.Now;
                    service.UpdateOne(content);
                    //提交至下一步
                    FlowService<RealEstateActualResultChangeFlow> flow = new FlowService<RealEstateActualResultChangeFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    RealEstateActualResultChangeProject project = RealEstateActualResultChangeProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
                }
                if (record.BusinessClass == "MeasurePutLinePreCheckFlow") {
                    //更新注册测绘师信息至contentinfo
                    PutLine_ContentInfo content = service.GetById<PutLine_ContentInfo>(record.ID);
                    content.SurveyMasterName = personName;
                    content.SurveyMasterNo = personNo;
                    content.SurveyMasterSureTime = DateTime.Now;
                    content.DataCheckState = 1;
                    service.UpdateOne(content);
                    //提交至下一步
                    FlowService<MeasurePutLinePreCheckFlow> flow = new FlowService<MeasurePutLinePreCheckFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    MeasurePutLinePreCheckProject project = MeasurePutLinePreCheckProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
                }
                if (record.BusinessClass == "RealEstatePreCheckSurveyAutoFlow") {
                    //更新注册测绘师信息至contentinfo
                    {
                        PreSurvey_ContentInfo content = service.GetById<PreSurvey_ContentInfo>(record.ID);
                        content.SurveyMasterName = personName;
                        content.SurveyMasterNo = personNo;
                        content.SurveyMasterSureTime = DateTime.Now;
                        service.UpdateOne(content);
                    }
                    {
                        PutLine_ContentInfo content = service.GetById<PutLine_ContentInfo>(record.ID);
                        content.SurveyMasterName = personName;
                        content.SurveyMasterNo = personNo;
                        content.SurveyMasterSureTime = DateTime.Now;
                        content.DataCheckState = 1;
                        service.UpdateOne(content);
                    }
                    //提交至下一步
                    FlowService<RealEstatePreCheckSurveyAutoFlow> flow = new FlowService<RealEstatePreCheckSurveyAutoFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    RealEstatePreCheckSurveyAutoProject project = RealEstatePreCheckSurveyAutoProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
                }
                if (record.BusinessClass == "CouncilPlanCheckFlow") {
                    //更新注册测绘师信息至contentinfo
                    CouncilPlanCheckContentInfo content = service.GetById<CouncilPlanCheckContentInfo>(record.ID);
                    content.SurveyMasterName = personName;
                    content.SurveyMasterNo = personNo;
                    content.SurveyMasterSureTime = DateTime.Now;
                    service.UpdateOne(content);
                    //提交至下一步
                    FlowService<CouncilPlanCheckFlow> flow = new FlowService<CouncilPlanCheckFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    CouncilPlanCheckProject project = CouncilPlanCheckProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
                }
                if (record.BusinessClass == "CouncilMeasurePutLinePreCheckFlow") {
                    //更新注册测绘师信息至contentinfo
                    PutLine_ContentInfo content = service.GetById<PutLine_ContentInfo>(record.ID);
                    content.SurveyMasterName = personName;
                    content.SurveyMasterNo = personNo;
                    content.SurveyMasterSureTime = DateTime.Now;
                    content.DataCheckState = 1;
                    service.UpdateOne(content);
                    //提交至下一步
                    FlowService<CouncilMeasurePutLinePreCheckFlow> flow = new FlowService<CouncilMeasurePutLinePreCheckFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    CouncilMeasurePutLinePreCheckProject project = CouncilMeasurePutLinePreCheckProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
                }
                if (record.BusinessClass == "RealEstateOverallActualSurveyFlow") {
                    //更新注册测绘师信息至contentinfo
                    EstateActualSurveyContentInfo content = service.GetById<EstateActualSurveyContentInfo>(record.ID);
                    content.SurveyMasterName = personName;
                    content.SurveyMasterNo = personNo;
                    content.SurveyMasterSureTime = DateTime.Now;
                    service.UpdateOne(content);
                    //提交至下一步
                    FlowService<RealEstateOverallActualSurveyFlow> flow = new FlowService<RealEstateOverallActualSurveyFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    RealEstateOverallActualSurveyProject project = RealEstateOverallActualSurveyProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
                }
                return new ApiResult() { StateCode = 0, Message = "待完善..." };
            }
            catch (Exception e) {
                return new ApiResult() { StateCode = 0, Message = "确认失败，错误信息：" + e.Message + "" };
            }
        }

        /// <summary>
        /// 征地拆迁MDB入库接口
        /// </summary>
        /// <param name="id">业务ID</param>
        /// <param name="layer">图层名称，可选参数，默认为"default"</param>
        /// <returns></returns>
        [HttpPost, Route("SubmitLandSurveyMdbToDatabase")]
        public async Task<ApiResult> SubmitLandSurveyMdbToDatabase(string id, string layer = "default") {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "请输入正确的业务ID" };
            }

            var record = service.GetById<BusinessBaseInfo>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到业务信息" };
            }

            // 仅支持征地拆迁业务类型
            if (record.BusinessClass != nameof(LandSurveyProjectFlow)) {
                return new ApiResult { StateCode = 0, Message = "该接口仅支持征地拆迁数据交汇业务" };
            }

            try {
                // 获取业务内容信息
                LandSurvey_ContentInfo content = service.GetById<LandSurvey_ContentInfo>(record.ID);
                if (content == null) {
                    return new ApiResult { StateCode = 0, Message = "无法找到业务内容信息" };
                }

                // 检查MDB检查是否已完成
                if (content.DataCheckState != 1) {
                    return new ApiResult { StateCode = 0, Message = "MDB检查尚未完成，无法提交入库" };
                }

                if (string.IsNullOrEmpty(content.DataCheckID)) {
                    return new ApiResult { StateCode = 0, Message = "无效的检查任务ID" };
                }

                // 获取当前用户信息
                var personNo = UserInfo.PersonNo;
                var personName = UserInfo.PersonName;

                // 更新注册测绘师信息至contentinfo
                content.SurveyMasterName = personName;
                content.SurveyMasterNo = personNo;
                content.SurveyMasterSureTime = DateTime.Now;
                service.UpdateOne(content);

                // 调用MDB入库接口
                if (string.IsNullOrEmpty(layer)) {
                    layer = "default";
                }

                string uploadSdeUrl = $"{MdbCheckApiUrl}/mdb/uploadsde/{content.DataCheckID}/{layer}";

                var httpClient = HttpClientFactory.Create();
                var response = await httpClient.PostAsync(uploadSdeUrl, new StringContent(""));

                if (!response.IsSuccessStatusCode) {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Request.WriteSDCLog("业务办理", $"征地拆迁MDB入库失败 >> 业务ID：{record.ID} >> 任务ID：{content.DataCheckID} >> 状态码：{response.StatusCode} >> 错误：{errorContent}");
                    return new ApiResult { StateCode = 0, Message = $"提交入库失败：{errorContent}" };
                }

                Request.WriteSDCLog("业务办理", $"征地拆迁MDB入库成功 >> 业务ID：{record.ID} >> 任务ID：{content.DataCheckID} >> 图层：{layer}");

                return new ApiResult { StateCode = 1, Message = "提交入库成功" };
            }
            catch (Exception ex) {
                Request.WriteSDCLog("业务办理", $"征地拆迁MDB入库失败 >> 业务ID：{record.ID} >> 错误：{ex.Message}");
                return new ApiResult { StateCode = 0, Message = $"提交入库失败：{ex.Message}" };
            }
        }

        /// <summary>
        /// 下载错误报告
        /// </summary>
        /// <param name="dataCheckId"></param>
        /// <returns></returns>
        [HttpGet, Route("DownloadErrorReport")]
        public async Task<HttpResponseMessage> DownloadErrorReport(string dataCheckId) {
            var url = ExternalApiConfig.EPSDownUrl + "/DownloadWordFile?strFileGuid=" + dataCheckId;
            var client = new HttpClient();
            var response = await client.GetAsync(url, HttpCompletionOption.ResponseHeadersRead);
            if (response.IsSuccessStatusCode) {
                response.Content.Headers.ContentType = MediaTypeHeaderValue.Parse("application/octet-stream");
                response.Content.Headers.ContentDisposition = new ContentDispositionHeaderValue("attachment") { FileName = "不动产预测绘成果检查报告.docx" };
            }

            return response;
        }
        /// <summary>
        /// 判断当前用户是否是测绘单位的注册测绘师
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("IsSurveyMaster")]
        public ApiResult IsSurveyMaster() {
            //获取调用者的身份
            var personNo = UserInfo.PersonNo;
            //根据身份证号判断是否为注册测绘师
            var employees = service.GetList<CompanyEmployees>($"PersonNumber='{personNo}' AND PersonRole='注册测绘师'");
            return new ApiResult() { StateCode = employees.Any() ? 1 : 0 };
        }
        /// <summary>
        /// 获取测绘成果确认列表
        /// </summary>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        /// <remarks>包含已确认和待确认的</remarks>
        [HttpGet, Route("GetSurveyProjectResultConfirmList")]
        public PageResult<SurveyProjectResultListItem> GetSurveyProjectResultConfirmList(int pageIndex = 1, int pageSize = 10) {
            //获取用户信息
            var UserId = UserInfo.UserId;
            var UserName = UserInfo.PersonName;
            var PersonNumber = UserInfo.PersonNo;
            if (string.IsNullOrWhiteSpace(UserId) || string.IsNullOrWhiteSpace(UserName) || string.IsNullOrWhiteSpace(PersonNumber)) {
                return null;
            }
            //获取绑定的测绘单位
            var employee = service.GetList<CompanyEmployees>($"PersonNumber='{PersonNumber}' AND PersonRole='注册测绘师'");
            if (!employee.Any()) {
                return null;
            }

            //组织SQL语句
            var mainSql = string.Format(SurveyProjectResultListItem.MainSql, UserName, PersonNumber);

            return service.GetPageResultWithSql<SurveyProjectResultListItem>(mainSql, "6 desc nulls first, 3", pageIndex, pageSize);
        }
        /// <summary>
        /// 根据ID获取测绘项目详情
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("GetSurveyProjectResultByID")]
        public ApiResult GetSurveyProjectResultByID(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "参数错误" };
            }

            var baseinfo = service.GetById<BusinessBaseInfo>(id);

            if (baseinfo == null) {
                return new ApiResult { StateCode = 0, Message = "该业务不存在" };
            }

            //根据流程，判断返回的详情信息
            if (baseinfo.BusinessClass == "RealEstatePreSurveyFlow") {
                //不动产预测绘
                var project = RealEstatePreSurveyProject.GetByBusinessID(id);
                if (project?.ContentInfo == null) {
                    return new ApiResult { StateCode = 0, Message = "获取业务信息失败，请稍后再试" };
                }

                var surveyResult = project.Attachments.Where(a =>
                        a.AttachmentType == "项目成果附件" && a.StateCode == 0 &&
                        (a.AttachmentCategories == "楼盘信息表" || a.AttachmentCategories == "不动产预核报告"))
                    .Select(a => new {
                        Name = a.AttachmentName,
                        AttachmentID = a.ID
                    }).ToList();
                return new ApiResult() {
                    StateCode = 1,
                    Message = "获取完成",
                    Data = new {
                        project.BaseInfo.ID,
                        Type = "PDF",
                        SurveyResult = surveyResult,
                        project.ContentInfo.SurveyMasterSureTime
                    }
                };
            }
            if (baseinfo.BusinessClass == "MeasurePutLineFlow") {
                //放线测量
                var project = MeasurePutLineProject.GetByBusinessID(id);
                if (project?.ContentInfo == null) {
                    return new ApiResult { StateCode = 0, Message = "获取业务信息失败，请稍后再试" };
                }
                var surveyResult = project.Attachments.Where(a => a.AttachmentType == "项目成果附件" && a.StateCode == 0 && a.AttachmentCategories == "放线报告")
                    .Select(a => new {
                        Name = "放线报告",
                        AttachmentID = a.ID
                    }).ToList();

                return new ApiResult() {
                    StateCode = 1,
                    Message = "获取完成",
                    Data = new {
                        project.BaseInfo.ID,
                        Type = "PDF",
                        SurveyResult = surveyResult,
                        project.ContentInfo.SurveyMasterSureTime
                    }
                };
            }
            //根据流程，判断返回的详情信息
            if (baseinfo.BusinessClass == "RealEstateActualSurveyFlow") {
                //不动产实测绘
                var project = RealEstateActualSurveyProject.GetByBusinessID(id);
                if (project?.ContentInfo == null) {
                    return new ApiResult { StateCode = 0, Message = "获取业务信息失败，请稍后再试" };
                }


                var surveyResult = project.Attachments.Where(a =>
                        a.AttachmentType == "项目成果附件" && a.StateCode == 0 &&
                        (a.AttachmentCategories == "楼盘信息表" || a.AttachmentCategories == "不动产实核报告" ||
                         a.AttachmentCategories == "竣工规划条件核实信息表"))
                    .Select(a => new {
                        Name = a.AttachmentName,
                        AttachmentID = a.ID
                    }).ToList();

                return new ApiResult() {
                    StateCode = 1,
                    Message = "获取完成",
                    Data = new {
                        project.BaseInfo.ID,
                        Type = "PDF",
                        SurveyResult = surveyResult,
                        project.ContentInfo.SurveyMasterSureTime
                    }
                };
            }
            if (baseinfo.BusinessClass == "PlanCheckSurveyFlow") {
                //规划核实测量
                //var project = PlanCheckSurveyProject.GetByBusinessID(id);
                //if (project?.ContentInfo == null) {
                //    return new ApiResult { StateCode = 0, Message = "获取业务信息失败，请稍后再试" };
                //}
                //return new ApiResult() {
                //    StateCode = 1,
                //    Message = "获取完成",
                //    Data = new {
                //        project.BaseInfo.ID,
                //        Type = "PDF",
                //        SurveyResult = default(object),
                //        project.ContentInfo.SurveyMasterSureTime
                //    }
                //};
            }
            //根据流程，判断返回的详情信息
            if (baseinfo.BusinessClass == "RealEstatePreSurveyBuildingTableChangeFlow") {
                //不动产预测绘
                var project = RealEstatePreSurveyBuildingTableChangeProject.GetByBusinessID(id);
                if (project?.ContentInfo == null) {
                    return new ApiResult { StateCode = 0, Message = "获取业务信息失败，请稍后再试" };
                }

                var surveyResult = project.Attachments.Where(a =>
                        a.AttachmentType == "项目成果附件" && a.StateCode == 0 &&
                        (a.AttachmentCategories == "楼盘信息表" || a.AttachmentCategories == "不动产预核报告"))
                    .Select(a => new {
                        Name = a.AttachmentName,
                        AttachmentID = a.ID
                    }).ToList();
                return new ApiResult() {
                    StateCode = 1,
                    Message = "获取完成",
                    Data = new {
                        project.BaseInfo.ID,
                        Type = "PDF",
                        SurveyResult = surveyResult,
                        project.ContentInfo.SurveyMasterSureTime
                    }
                };
            }
            //根据流程，判断返回的详情信息
            if (baseinfo.BusinessClass == "RealEstatePreSurveyResultChangeFlow") {
                //不动产预测绘
                var project = RealEstatePreSurveyResultChangeProject.GetByBusinessID(id);
                if (project?.ContentInfo == null) {
                    return new ApiResult { StateCode = 0, Message = "获取业务信息失败，请稍后再试" };
                }

                var surveyResult = project.Attachments.Where(a =>
                        a.AttachmentType == "项目成果附件" && a.StateCode == 0 &&
                        (a.AttachmentCategories == "楼盘信息表" || a.AttachmentCategories == "不动产预核报告"))
                    .Select(a => new {
                        Name = a.AttachmentName,
                        AttachmentID = a.ID
                    }).ToList();
                return new ApiResult() {
                    StateCode = 1,
                    Message = "获取完成",
                    Data = new {
                        project.BaseInfo.ID,
                        Type = "PDF",
                        SurveyResult = surveyResult,
                        project.ContentInfo.SurveyMasterSureTime
                    }
                };
            }
            //根据流程，判断返回的详情信息
            if (baseinfo.BusinessClass == "RealEstateActualBuildingTableChangeFlow") {
                //不动产实测绘
                var project = RealEstateActualBuildingTableChangeProject.GetByBusinessID(id);
                if (project?.ContentInfo == null) {
                    return new ApiResult { StateCode = 0, Message = "获取业务信息失败，请稍后再试" };
                }


                var surveyResult = project.Attachments.Where(a =>
                        a.AttachmentType == "项目成果附件" && a.StateCode == 0 &&
                        (a.AttachmentCategories == "楼盘信息表" || a.AttachmentCategories == "不动产实核报告" ||
                         a.AttachmentCategories == "竣工规划条件核实信息表"))
                    .Select(a => new {
                        Name = a.AttachmentName,
                        AttachmentID = a.ID
                    }).ToList();

                return new ApiResult() {
                    StateCode = 1,
                    Message = "获取完成",
                    Data = new {
                        project.BaseInfo.ID,
                        Type = "PDF",
                        SurveyResult = surveyResult,
                        project.ContentInfo.SurveyMasterSureTime
                    }
                };
            }
            //根据流程，判断返回的详情信息
            if (baseinfo.BusinessClass == "RealEstateActualResultChangeFlow") {
                //不动产实测绘
                var project = RealEstateActualResultChangeProject.GetByBusinessID(id);
                if (project?.ContentInfo == null) {
                    return new ApiResult { StateCode = 0, Message = "获取业务信息失败，请稍后再试" };
                }


                var surveyResult = project.Attachments.Where(a =>
                        a.AttachmentType == "项目成果附件" && a.StateCode == 0 &&
                        (a.AttachmentCategories == "楼盘信息表" || a.AttachmentCategories == "不动产实核报告" ||
                         a.AttachmentCategories == "竣工规划条件核实信息表"))
                    .Select(a => new {
                        Name = a.AttachmentName,
                        AttachmentID = a.ID
                    }).ToList();

                return new ApiResult() {
                    StateCode = 1,
                    Message = "获取完成",
                    Data = new {
                        project.BaseInfo.ID,
                        Type = "PDF",
                        SurveyResult = surveyResult,
                        project.ContentInfo.SurveyMasterSureTime
                    }
                };
            }
            //根据流程，判断返回的详情信息
            if (baseinfo.BusinessClass == "MeasurePutLinePreCheckFlow") {
                //放线测量
                var project = MeasurePutLinePreCheckProject.GetByBusinessID(id);
                if (project?.ContentInfo == null) {
                    return new ApiResult { StateCode = 0, Message = "获取业务信息失败，请稍后再试" };
                }
                var surveyResult = project.Attachments.Where(a => a.AttachmentType == "项目成果附件" && a.StateCode == 0 && a.AttachmentCategories == "放线报告")
                    .Select(a => new {
                        Name = "放线报告",
                        AttachmentID = a.ID
                    }).ToList();

                return new ApiResult() {
                    StateCode = 1,
                    Message = "获取完成",
                    Data = new {
                        project.BaseInfo.ID,
                        Type = "PDF",
                        SurveyResult = surveyResult,
                        project.ContentInfo.SurveyMasterSureTime
                    }
                };
            }
            //根据流程，判断返回的详情信息
            if (baseinfo.BusinessClass == "CouncilPlanCheckFlow") {
                //不动产实测绘
                var project = CouncilPlanCheckProject.GetByBusinessID(id);
                if (project?.ContentInfo == null) {
                    return new ApiResult { StateCode = 0, Message = "获取业务信息失败，请稍后再试" };
                }


                var surveyResult = project.Attachments.Where(a =>
                        a.AttachmentType == "项目成果附件" && a.StateCode == 0 &&
                        (a.AttachmentCategories == "楼盘信息表" || a.AttachmentCategories == "不动产实核报告" ||
                         a.AttachmentCategories == "竣工规划条件核实信息表"))
                    .Select(a => new {
                        Name = a.AttachmentName,
                        AttachmentID = a.ID
                    }).ToList();

                return new ApiResult() {
                    StateCode = 1,
                    Message = "获取完成",
                    Data = new {
                        project.BaseInfo.ID,
                        Type = "PDF",
                        SurveyResult = surveyResult,
                        project.ContentInfo.SurveyMasterSureTime
                    }
                };
            }
            //根据流程，判断返回的详情信息
            if (baseinfo.BusinessClass == "CouncilMeasurePutLinePreCheckFlow") {
                //放线测量
                var project = CouncilMeasurePutLinePreCheckProject.GetByBusinessID(id);
                if (project?.ContentInfo == null) {
                    return new ApiResult { StateCode = 0, Message = "获取业务信息失败，请稍后再试" };
                }
                var surveyResult = project.Attachments.Where(a => a.AttachmentType == "项目成果附件" && a.StateCode == 0 && a.AttachmentCategories == "放线报告")
                    .Select(a => new {
                        Name = "放线报告",
                        AttachmentID = a.ID
                    }).ToList();

                return new ApiResult() {
                    StateCode = 1,
                    Message = "获取完成",
                    Data = new {
                        project.BaseInfo.ID,
                        Type = "PDF",
                        SurveyResult = surveyResult,
                        project.ContentInfo.SurveyMasterSureTime
                    }
                };
            }
            //根据流程，判断返回的详情信息
            if (baseinfo.BusinessClass == "RealEstateOverallActualSurveyFlow") {
                //不动产实测绘
                var project = RealEstateOverallActualSurveyProject.GetByBusinessID(id);
                if (project?.ContentInfo == null) {
                    return new ApiResult { StateCode = 0, Message = "获取业务信息失败，请稍后再试" };
                }


                var surveyResult = project.Attachments.Where(a =>
                        a.AttachmentType == "项目成果附件" && a.StateCode == 0 &&
                        (a.AttachmentCategories == "楼盘信息表" || a.AttachmentCategories == "不动产实核报告" ||
                         a.AttachmentCategories == "竣工规划条件核实信息表"))
                    .Select(a => new {
                        Name = a.AttachmentName,
                        AttachmentID = a.ID
                    }).ToList();

                return new ApiResult() {
                    StateCode = 1,
                    Message = "获取完成",
                    Data = new {
                        project.BaseInfo.ID,
                        Type = "PDF",
                        SurveyResult = surveyResult,
                        project.ContentInfo.SurveyMasterSureTime
                    }
                };
            } else {
                var business = service.GetById<BusinessBaseInfo>(id);
                if (business != null) {
                    var attachments = service.GetList<AttachmentInfo>($"BusinessId='{id}' And AttachmentType='项目成果附件' AND StateCode=0");
                    return new ApiResult() {
                        StateCode = 1,
                        Message = "获取完成",
                        Data = new {
                            ID = id,
                            Type = "PDF",
                            SurveyResult = attachments.Where(a => a.AttachmentExt == ".pdf").Select(a => new {
                                Name = a.AttachmentCategories,
                                AttachmentID = a.ID
                            })
                        }
                    };
                }
            }
            return new ApiResult { StateCode = 0, Message = "暂不支持的业务类型" };

        }
        /// <summary>
        /// 获取PDF附件
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("GetPDFAttachment"), AllowAnonymous]
        public HttpResponseMessage GetPDFAttachment(string id) {
            var response = new AttachmentManageController().AttachmentDownloadRequest(id);
            if (response.IsSuccessStatusCode) {
                response.Content.Headers.ContentType.MediaType = "application/pdf";
                response.Content.Headers.ContentDisposition.DispositionType = "inline";
                response.Content.Headers.ContentDisposition.FileName = "成果附件.pdf";
            }
            return response;
        }
        /// <summary>
        /// 使用注册测绘师授权确认测绘成果，只有本单位的人员才能调用此接口
        /// </summary>
        /// <param name="id"></param>
        /// <param name="actionId"></param>
        /// <param name="authid"></param>
        /// <returns></returns>
        [HttpPost, Route("ConfirmSurveyResultUsingSurveyMasterAuth")]
        public ApiResult ConfirmSurveyResultUsingSurveyMasterAuth(string id, string actionId, string authid, string completionLandArea = null, string completionBuildingArea = null) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "请输入正确的信息，参数id不能为空" };
            }
            if (string.IsNullOrWhiteSpace(actionId)) {
                return new ApiResult { StateCode = 0, Message = "请输入正确的信息，参数actionId不能为空" };
            }
            if (string.IsNullOrWhiteSpace(authid)) {
                return new ApiResult { StateCode = 0, Message = "请输入正确的信息，参数authid不能为空" };
            }
            var business = service.GetById<BusinessBaseInfo>(id);
            if (business == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到业务信息" };
            }

            //只有本测绘单位的人员才能调用此接口
            var company = UserInfo.GetRelateSurveyCompany();
            if (company == null) {
                return new ApiResult { StateCode = 0, Message = "您不是测绘单位的人员" };
            }
            if (company.CreditCode != business.SurveyCompanyNo) {
                return new ApiResult { StateCode = 0, Message = "您不是此业务受委托的测绘单位的人员" };
            }

            var action = service.GetById<BusinessLinkInfo>(actionId);
            if (action?.StateCode == 2) {
                return new ApiResult { StateCode = 0, Message = "该环节已完成，请勿重复操作" };
            }

            if (business.StateCode == 0) {
                //先签收业务
                AcceptBusiness(id);
            }
            string cannotFlowPostMessage;
            if (!CanFlowPost(business, actionId, UserInfo.UserId, out cannotFlowPostMessage)) {
                return new ApiResult { StateCode = 0, Message = $"操作失败，{cannotFlowPostMessage}" };
            }

            var smAuthModel = SurveyMasterAuthRequest.GetFromAuthId(authid, service);
            if (smAuthModel == null) {
                return new ApiResult { StateCode = 0, Message = "无法获取注册测绘师授权信息" };
            }
            if (smAuthModel.StateCode != 1) {
                return new ApiResult { StateCode = 0, Message = "注册测绘师的授权信息已失效或未生效" };
            }
            if (smAuthModel.CompanyID != company.ID) {
                return new ApiResult { StateCode = 0, Message = "此注册测绘师的授权信息不属于受托的测绘单位，不能执行此操作" };
            }
            var surveyMaster = service.GetById<CompanyEmployees>(smAuthModel.SurveyMasterID);
            if (surveyMaster == null) {
                return new ApiResult { StateCode = 0, Message = "无法获取到注册测绘师信息" };
            }
            if (!smAuthModel.BusinessClasses.Select(c => c.BusinessClass).Contains(business.BusinessClass)) {
                return new ApiResult { StateCode = 0, Message = "此注册测绘师的授权信息不包含此业务类型，不能执行此操作" };
            }
            HttpRequestBase req = ((HttpContextBase)Request.Properties["MS_HttpContext"]).Request;
            var reqInfoHelper = new HttpRequestInfoHelper(req);
            LogService.WriteLogs(Request, "业务管理", $"使用授权确认测绘成果 >> 业务ID：{business.ID} >> 业务类型：{business.BusinessType} >> 授权ID：{authid} >> 注册测绘师：{surveyMaster.PersonName}[{surveyMaster.PersonNumber}]", reqInfoHelper);
            if (business.BusinessClass == "MeasurePutLineFlow") {
                //更新注册测绘师信息至contentinfo
                PutLine_ContentInfo content = service.GetById<PutLine_ContentInfo>(business.ID);
                content.SurveyMasterName = surveyMaster.PersonName;
                content.SurveyMasterNo = surveyMaster.PersonNumber;
                content.SurveyMasterSureTime = DateTime.Now;
                content.DataCheckState = 1;
                service.UpdateOne(content);
                //提交至下一步
                FlowService<MeasurePutLineFlow> flow = new FlowService<MeasurePutLineFlow>(service);
                flow.FlowPostToNext(business.ID);
                MeasurePutLineProject project = MeasurePutLineProject.GetByBusinessID(id);
                return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
            }
            if (business.BusinessClass == "RealEstatePreSurveyFlow") {
                //更新注册测绘师信息至contentinfo
                PreSurvey_ContentInfo content = service.GetById<PreSurvey_ContentInfo>(business.ID);
                content.SurveyMasterName = surveyMaster.PersonName;
                content.SurveyMasterNo = surveyMaster.PersonNumber;
                content.SurveyMasterSureTime = DateTime.Now;
                service.UpdateOne(content);
                //提交至下一步
                FlowService<RealEstatePreSurveyFlow> flow = new FlowService<RealEstatePreSurveyFlow>(service);
                flow.FlowPostToNext(business.ID);
                RealEstatePreSurveyProject project = RealEstatePreSurveyProject.GetByBusinessID(id);
                return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
            }
            if (business.BusinessClass == "RealEstatePreCheckSurveyFlow") {
                //更新注册测绘师信息至contentinfo
                {
                    PreSurvey_ContentInfo content = service.GetById<PreSurvey_ContentInfo>(business.ID);
                    content.SurveyMasterName = surveyMaster.PersonName;
                    content.SurveyMasterNo = surveyMaster.PersonNumber;
                    content.SurveyMasterSureTime = DateTime.Now;
                    service.UpdateOne(content);
                }
                {
                    PutLine_ContentInfo content = service.GetById<PutLine_ContentInfo>(business.ID);
                    content.SurveyMasterName = surveyMaster.PersonName;
                    content.SurveyMasterNo = surveyMaster.PersonNumber;
                    content.SurveyMasterSureTime = DateTime.Now;
                    content.DataCheckState = 1;
                    service.UpdateOne(content);
                }
                //提交至下一步
                FlowService<RealEstatePreCheckSurveyFlow> flow = new FlowService<RealEstatePreCheckSurveyFlow>(service);
                flow.FlowPostToNext(business.ID);
                RealEstatePreCheckSurveyProject project = RealEstatePreCheckSurveyProject.GetByBusinessID(id);
                return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
            }
            if (business.BusinessClass == "BlueLineSurveyFlow") {
                //更新注册测绘师信息至contentinfo
                BlueLineSurveyContentInfo content = service.GetById<BlueLineSurveyContentInfo>(business.ID);
                content.SurveyMasterName = surveyMaster.PersonName;
                content.SurveyMasterNo = surveyMaster.PersonNumber;
                content.SurveyMasterSureTime = DateTime.Now;
                service.UpdateOne(content);
                //提交至下一步
                FlowService<BlueLineSurveyFlow> flow = new FlowService<BlueLineSurveyFlow>(service);
                flow.FlowPostToNext(business.ID);
                BlueLineSurveyProject project = BlueLineSurveyProject.GetByBusinessID(id);
                return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
            }
            if (business.BusinessClass == "MarkPointSurveyFlow") {
                //更新注册测绘师信息至contentinfo
                MarkPointSurveyContentInfo content = service.GetById<MarkPointSurveyContentInfo>(business.ID);
                if (content.DataCheckState != 1) {
                    return new ApiResult { StateCode = 0, Message = "测绘成果未检查通过" };
                }
                content.SurveyMasterName = surveyMaster.PersonName;
                content.SurveyMasterNo = surveyMaster.PersonNumber;
                content.SurveyMasterSureTime = DateTime.Now;
                service.UpdateOne(content);
                //提交至下一步
                FlowService<MarkPointSurveyFlow> flow = new FlowService<MarkPointSurveyFlow>(service);
                flow.FlowPostToNext(business.ID);
                MarkPointSurveyProject project = MarkPointSurveyProject.GetByBusinessID(id);
                return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
            }
            if (business.BusinessClass == "PlanCheckSurveyFlow") {
                //更新注册测绘师信息至contentinfo
                //PlanCheckSurveyContentInfo content = service.GetById<PlanCheckSurveyContentInfo>(business.ID);
                //content.SurveyMasterName = surveyMaster.PersonName;
                //content.SurveyMasterNo = surveyMaster.PersonNumber;
                //content.SurveyMasterSureTime = DateTime.Now;
                //content.DataCheckState = 1;
                //service.UpdateOne(content);
                ////提交至下一步
                //FlowService<PlanCheckSurveyFlow> flow = new FlowService<PlanCheckSurveyFlow>(service);
                //flow.FlowPostToNext(business.ID);
                //PlanCheckSurveyProject project = PlanCheckSurveyProject.GetByBusinessID(id);
                //return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
                return new ApiResult { StateCode = 0, Message = "操作失败，暂不支持该业务" };

            }
            if (business.BusinessClass == "RealEstateActualSurveyFlow") {
                //更新注册测绘师信息至contentinfo
                EstateActualSurveyContentInfo content = service.GetById<EstateActualSurveyContentInfo>(business.ID);
                content.SurveyMasterName = surveyMaster.PersonName;
                content.SurveyMasterNo = surveyMaster.PersonNumber;
                content.SurveyMasterSureTime = DateTime.Now;
                content.CompletionLandArea = completionLandArea;
                content.CompletionBuildingArea = completionBuildingArea;
                service.UpdateOne(content);
                //提交至下一步
                FlowService<RealEstateActualSurveyFlow> flow = new FlowService<RealEstateActualSurveyFlow>(service);
                flow.FlowPostToNext(business.ID);
                RealEstateActualSurveyProject project = RealEstateActualSurveyProject.GetByBusinessID(id);
                return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
            }
            if (business.BusinessClass == "RealEstatePreSurveyBuildingTableChangeFlow") {
                //更新注册测绘师信息至contentinfo
                PreSurvey_ContentInfo content = service.GetById<PreSurvey_ContentInfo>(business.ID);
                content.SurveyMasterName = surveyMaster.PersonName;
                content.SurveyMasterNo = surveyMaster.PersonNumber;
                content.SurveyMasterSureTime = DateTime.Now;
                service.UpdateOne(content);
                //提交至下一步
                FlowService<RealEstatePreSurveyBuildingTableChangeFlow> flow = new FlowService<RealEstatePreSurveyBuildingTableChangeFlow>(service);
                flow.FlowPostToNext(business.ID);
                RealEstatePreSurveyBuildingTableChangeProject project = RealEstatePreSurveyBuildingTableChangeProject.GetByBusinessID(id);
                return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
            }
            if (business.BusinessClass == "RealEstatePreSurveyResultChangeFlow") {
                //更新注册测绘师信息至contentinfo
                PreSurvey_ContentInfo content = service.GetById<PreSurvey_ContentInfo>(business.ID);
                content.SurveyMasterName = surveyMaster.PersonName;
                content.SurveyMasterNo = surveyMaster.PersonNumber;
                content.SurveyMasterSureTime = DateTime.Now;
                content.CompletionLandArea = completionLandArea;
                content.CompletionBuildingArea = completionBuildingArea;
                service.UpdateOne(content);
                //提交至下一步
                FlowService<RealEstatePreSurveyResultChangeFlow> flow = new FlowService<RealEstatePreSurveyResultChangeFlow>(service);
                flow.FlowPostToNext(business.ID);
                RealEstatePreSurveyResultChangeProject project = RealEstatePreSurveyResultChangeProject.GetByBusinessID(id);
                return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
            }
            if (business.BusinessClass == "RealEstateActualBuildingTableChangeFlow") {
                //更新注册测绘师信息至contentinfo
                EstateActualSurveyContentInfo content = service.GetById<EstateActualSurveyContentInfo>(business.ID);
                content.SurveyMasterName = surveyMaster.PersonName;
                content.SurveyMasterNo = surveyMaster.PersonNumber;
                content.SurveyMasterSureTime = DateTime.Now;
                service.UpdateOne(content);
                //提交至下一步
                FlowService<RealEstateActualBuildingTableChangeFlow> flow = new FlowService<RealEstateActualBuildingTableChangeFlow>(service);
                flow.FlowPostToNext(business.ID);
                RealEstateActualBuildingTableChangeProject project = RealEstateActualBuildingTableChangeProject.GetByBusinessID(id);
                return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
            }
            if (business.BusinessClass == "RealEstateActualResultChangeFlow") {
                //更新注册测绘师信息至contentinfo
                EstateActualSurveyContentInfo content = service.GetById<EstateActualSurveyContentInfo>(business.ID);
                content.SurveyMasterName = surveyMaster.PersonName;
                content.SurveyMasterNo = surveyMaster.PersonNumber;
                content.SurveyMasterSureTime = DateTime.Now;
                content.CompletionLandArea = completionLandArea;
                content.CompletionBuildingArea = completionBuildingArea;
                service.UpdateOne(content);
                //提交至下一步
                FlowService<RealEstateActualResultChangeFlow> flow = new FlowService<RealEstateActualResultChangeFlow>(service);
                flow.FlowPostToNext(business.ID);
                RealEstateActualResultChangeProject project = RealEstateActualResultChangeProject.GetByBusinessID(id);
                return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
            }
            if (business.BusinessClass == "MeasurePutLinePreCheckFlow") {
                //更新注册测绘师信息至contentinfo
                PutLine_ContentInfo content = service.GetById<PutLine_ContentInfo>(business.ID);
                content.SurveyMasterName = surveyMaster.PersonName;
                content.SurveyMasterNo = surveyMaster.PersonNumber;
                content.SurveyMasterSureTime = DateTime.Now;
                content.CompletionLandArea = completionLandArea;
                content.CompletionBuildingArea = completionBuildingArea;
                content.DataCheckState = 1;
                service.UpdateOne(content);
                //提交至下一步
                FlowService<MeasurePutLinePreCheckFlow> flow = new FlowService<MeasurePutLinePreCheckFlow>(service);
                flow.FlowPostToNext(business.ID);
                MeasurePutLinePreCheckProject project = MeasurePutLinePreCheckProject.GetByBusinessID(id);
                return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
            }

            if (business.BusinessClass == "RealEstatePreCheckSurveyAutoFlow") {
                //更新注册测绘师信息至contentinfo
                {
                    PreSurvey_ContentInfo content = service.GetById<PreSurvey_ContentInfo>(business.ID);
                    content.SurveyMasterName = surveyMaster.PersonName;
                    content.SurveyMasterNo = surveyMaster.PersonNumber;
                    content.SurveyMasterSureTime = DateTime.Now;
                    service.UpdateOne(content);
                }
                {
                    PutLine_ContentInfo content = service.GetById<PutLine_ContentInfo>(business.ID);
                    content.SurveyMasterName = surveyMaster.PersonName;
                    content.SurveyMasterNo = surveyMaster.PersonNumber;
                    content.SurveyMasterSureTime = DateTime.Now;
                    content.DataCheckState = 1;
                    service.UpdateOne(content);
                }
                //提交至下一步
                FlowService<RealEstatePreCheckSurveyAutoFlow> flow = new FlowService<RealEstatePreCheckSurveyAutoFlow>(service);
                flow.FlowPostToNext(business.ID);
                RealEstatePreCheckSurveyAutoProject project = RealEstatePreCheckSurveyAutoProject.GetByBusinessID(id);
                return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
            }

            if (business.BusinessClass == "CouncilPlanCheckFlow") {
                //更新注册测绘师信息至contentinfo
                CouncilPlanCheckContentInfo content = service.GetById<CouncilPlanCheckContentInfo>(business.ID);

                //判断是否有上传DWG文件或者上传MDB检测通过
                bool success = false;
                if (!string.IsNullOrWhiteSpace(content.DataCheckID) && content.DataCheckState == 1) {
                    success = true;
                }

                if (success == false) {
                    success = service
                        .GetList<AttachmentInfo>(
                            $"BusinessID='{business.ID}' AND AttachmentCategories='DWG格式测绘成果' AND STATECODE=0").Any();
                    if (success) {
                        content.DataCheckState = 1;
                    }
                }

                if (success == false) {
                    return new ApiResult() {
                        StateCode = 0,
                        Message = "测绘成果未检查通过"
                    };
                }

                content.SurveyMasterName = surveyMaster.PersonName;
                content.SurveyMasterNo = surveyMaster.PersonNumber;
                content.SurveyMasterSureTime = DateTime.Now;
                content.CompletionLandArea = completionLandArea;
                content.CompletionBuildingArea = completionBuildingArea;
                service.UpdateOne(content);
                //提交至下一步
                FlowService<CouncilPlanCheckFlow> flow = new FlowService<CouncilPlanCheckFlow>(service);
                flow.FlowPostToNext(business.ID);
                CouncilPlanCheckProject project = CouncilPlanCheckProject.GetByBusinessID(id);
                return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
            }

            if (business.BusinessClass == "CouncilMeasurePutLinePreCheckFlow") {
                //更新注册测绘师信息至contentinfo
                PutLine_ContentInfo content = service.GetById<PutLine_ContentInfo>(business.ID);
                content.SurveyMasterName = surveyMaster.PersonName;
                content.SurveyMasterNo = surveyMaster.PersonNumber;
                content.SurveyMasterSureTime = DateTime.Now;
                content.DataCheckState = 1;
                service.UpdateOne(content);
                //提交至下一步
                FlowService<CouncilMeasurePutLinePreCheckFlow> flow = new FlowService<CouncilMeasurePutLinePreCheckFlow>(service);
                flow.FlowPostToNext(business.ID);
                CouncilMeasurePutLinePreCheckProject project = CouncilMeasurePutLinePreCheckProject.GetByBusinessID(id);
                return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
            }
            if (business.BusinessClass == "RealEstateOverallActualSurveyFlow") {
                //更新注册测绘师信息至contentinfo
                EstateActualSurveyContentInfo content = service.GetById<EstateActualSurveyContentInfo>(business.ID);
                content.SurveyMasterName = surveyMaster.PersonName;
                content.SurveyMasterNo = surveyMaster.PersonNumber;
                content.SurveyMasterSureTime = DateTime.Now;
                content.CompletionLandArea = completionLandArea;
                content.CompletionBuildingArea = completionBuildingArea;
                service.UpdateOne(content);
                //提交至下一步
                FlowService<RealEstateOverallActualSurveyFlow> flow = new FlowService<RealEstateOverallActualSurveyFlow>(service);
                flow.FlowPostToNext(business.ID);
                RealEstateOverallActualSurveyProject project = RealEstateOverallActualSurveyProject.GetByBusinessID(id);
                return new ApiResult { StateCode = 1, Data = project, Message = "确认成功" };
            }

            return new ApiResult { StateCode = 0, Message = "加班开发中..." };
        }
        #endregion


        #region 通用接口

        /// <summary>
        /// 业务签收
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost, Route("AcceptBusiness")]
        public ApiResult AcceptBusiness(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "ID不能为空，请传入正确的参数" };
            }
            List<BusinessLinkInfo> list = service.GetList<BusinessLinkInfo>("BusinessID='" + id + "' and StateCode IN(0,1,3)");
            if (list.Count == 0) {
                return new ApiResult { StateCode = 0, Message = "签收失败，无相关待签收业务" };
            }

            if (list.Count > 1) {
                return new ApiResult { StateCode = 0, Message = "签收失败，此业务有多个环节待签收" };
            }
            try {
                var userId = UserInfo.UserId;
                if (list.Count(a => a.StateCode == 1) == 1) {
                    if (list.First(a => a.StateCode == 1).CurrentUserID == userId) {
                        return new ApiResult { StateCode = 1, Message = "该业务已签收" };
                    }
                    else {
                        return new ApiResult { StateCode = 0, Message = "签收失败，该业务已由他人签收" };
                    }

                }
                var action = list.First();
                action.StateCode = 1;
                action.SignatureTime = DateTime.Now;
                action.CurrentUserID = userId;
                service.UpdateOne(action);
                var record = service.GetById<BusinessBaseInfo>(id);
                record.StateCode = 1;
                service.UpdateOne(record);//同步业务基本信息为已签收
                //移除签收列表索引
                service.DeleteMany<BusinessLinkToSign>($"BusinessLinkInfoID='{action.ID}'");
                Request.WriteSDCLog("业务办理", $"业务签收 >> 业务ID：{record.ID} >> 业务类型：{record.BusinessType} ");
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 1, Message = "签收失败，错误信息：" + e.Message + "" };
            }
            return new ApiResult { StateCode = 1, Message = "签收成功" };
        }

        /// <summary>
        /// 业务提交
        /// </summary>
        /// <param name="id"></param>
        /// <param name="actionId"></param>
        /// <returns></returns>
        [HttpPost, Route("SubmitBusiness")]
        public ApiResult SubmitBusiness(string id, string actionId) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "ID不能为空，请传入正确的参数" };
            }
            var record = service.GetById<BusinessBaseInfo>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "保存失败，无法找到业务信息" };
            }
            var userId = UserInfo.UserId;
            string cannotFlowPostMessage;
            if (!CanFlowPost(record, actionId, userId, out cannotFlowPostMessage)) {
                return new ApiResult { StateCode = 0, Message = $"操作失败，{cannotFlowPostMessage}" };
            }
            try {
                Request.WriteSDCLog("业务办理", $"业务提交 >> 业务ID：{record.ID} >> 业务类型：{record.BusinessType} ");

                if (record.BusinessClass == "BaseSurveyDataDownloadFlow") {
                    FlowService<BaseSurveyDataDownloadFlow> flow = new FlowService<BaseSurveyDataDownloadFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    BaseSurveyDataDownloadProject project = BaseSurveyDataDownloadProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "提交成功" };
                }
                if (record.BusinessClass == "MeasurePutLineFlow") {
                    if (actionId == "0") {
                        PutLine_ContentInfo pc = service.GetById<PutLine_ContentInfo>(id);
                        var rec = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(pc.ProjectPlanPermission);
                        var sel = rec.Select(r => CanProjectPlanPermissionUse(id, "MeasurePutLineFlow", r.Code));
                        if (sel.Any(s => s != "")) {
                            return new ApiResult { StateCode = 0, Message = string.Join(";", sel.Where(s => s != "").Distinct()) };
                        }
                    }
                    FlowService<MeasurePutLineFlow> flow = new FlowService<MeasurePutLineFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    MeasurePutLineProject project = MeasurePutLineProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "提交成功" };
                }
                if (record.BusinessClass == "RealEstatePreSurveyFlow") {
                    if (actionId == "0") {
                        PreSurvey_ContentInfo pc = service.GetById<PreSurvey_ContentInfo>(id);
                        var rec = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(pc.ProjectPlanPermission);
                        var sel = rec.Select(r => CanProjectPlanPermissionUse(id, "RealEstatePreSurveyFlow", r.Code));
                        if (sel.Any(s => s != "")) {
                            return new ApiResult { StateCode = 0, Message = string.Join(";", sel.Where(s => s != "").Distinct()) };
                        }
                        var selCheck = rec.Select(r => CanProjectPlanPermissionUse(id, "RealEstatePreCheckSurveyFlow", r.Code));
                        if (selCheck.Any(s => s != "")) {
                            return new ApiResult { StateCode = 0, Message = string.Join(";", selCheck.Where(s => s != "").Distinct()) };
                        }
                    }
                    FlowService<RealEstatePreSurveyFlow> flow = new FlowService<RealEstatePreSurveyFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    RealEstatePreSurveyProject project = RealEstatePreSurveyProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "提交成功" };
                }
                if (record.BusinessClass == "RealEstatePreCheckSurveyFlow") {
                    if (actionId == "0") {
                        PreSurvey_ContentInfo pc = service.GetById<PreSurvey_ContentInfo>(id);
                        var rec = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(pc.ProjectPlanPermission);
                        var sel = rec.Select(r => CanProjectPlanPermissionUse(id, "RealEstatePreSurveyFlow", r.Code));
                        if (sel.Any(s => s != "")) {
                            return new ApiResult { StateCode = 0, Message = string.Join(";", sel.Where(s => s != "").Distinct()) };
                        }
                        var selCheck = rec.Select(r => CanProjectPlanPermissionUse(id, "RealEstatePreCheckSurveyFlow", r.Code));
                        if (selCheck.Any(s => s != "")) {
                            return new ApiResult { StateCode = 0, Message = string.Join(";", selCheck.Where(s => s != "").Distinct()) };
                        }
                    }
                    FlowService<RealEstatePreCheckSurveyFlow> flow = new FlowService<RealEstatePreCheckSurveyFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    RealEstatePreCheckSurveyProject project = RealEstatePreCheckSurveyProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "提交成功" };
                }
                if (record.BusinessClass == "BlueLineSurveyFlow") {
                    FlowService<BlueLineSurveyFlow> flow = new FlowService<BlueLineSurveyFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    BlueLineSurveyProject project = BlueLineSurveyProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "提交成功" };
                }
                if (record.BusinessClass == "MarkPointSurveyFlow") {
                    FlowService<MarkPointSurveyFlow> flow = new FlowService<MarkPointSurveyFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    MarkPointSurveyProject project = MarkPointSurveyProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "提交成功" };
                }
                if (record.BusinessClass == "PlanCheckSurveyFlow") {
                    //FlowService<PlanCheckSurveyFlow> flow = new FlowService<PlanCheckSurveyFlow>(service);
                    //flow.FlowPostToNext(record.ID);
                    //PlanCheckSurveyProject project = PlanCheckSurveyProject.GetByBusinessID(id);
                    //return new ApiResult { StateCode = 1, Data = project, Message = "提交成功" };
                    return new ApiResult { StateCode = 0, Message = "提交失败，错误信息：暂不支持该业务" };
                }
                if (record.BusinessClass == "RealEstateActualSurveyFlow") {
                    FlowService<RealEstateActualSurveyFlow> flow = new FlowService<RealEstateActualSurveyFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    RealEstateActualSurveyProject project = RealEstateActualSurveyProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "提交成功" };
                }
                if (record.BusinessClass == "RealEstatePreSurveyBuildingTableChangeFlow") {
                    FlowService<RealEstatePreSurveyBuildingTableChangeFlow> flow = new FlowService<RealEstatePreSurveyBuildingTableChangeFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    RealEstatePreSurveyBuildingTableChangeProject project = RealEstatePreSurveyBuildingTableChangeProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "提交成功" };
                }
                if (record.BusinessClass == "RealEstatePreSurveyResultChangeFlow") {
                    FlowService<RealEstatePreSurveyResultChangeFlow> flow = new FlowService<RealEstatePreSurveyResultChangeFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    RealEstatePreSurveyResultChangeProject project = RealEstatePreSurveyResultChangeProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "提交成功" };
                }
                if (record.BusinessClass == "RealEstateActualBuildingTableChangeFlow") {
                    FlowService<RealEstateActualBuildingTableChangeFlow> flow = new FlowService<RealEstateActualBuildingTableChangeFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    RealEstateActualBuildingTableChangeProject project = RealEstateActualBuildingTableChangeProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "提交成功" };
                }
                if (record.BusinessClass == "RealEstateActualResultChangeFlow") {
                    FlowService<RealEstateActualResultChangeFlow> flow = new FlowService<RealEstateActualResultChangeFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    RealEstateActualResultChangeProject project = RealEstateActualResultChangeProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "提交成功" };
                }
                if (record.BusinessClass == "MeasurePutLinePreCheckFlow") {
                    if (actionId == "0") {
                        PutLine_ContentInfo pc = service.GetById<PutLine_ContentInfo>(id);
                        var rec = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(pc.ProjectPlanPermission);
                        var sel = rec.Select(r => CanProjectPlanPermissionUse(id, "MeasurePutLinePreCheckFlow", r.Code));
                        if (sel.Any(s => s != "")) {
                            return new ApiResult { StateCode = 0, Message = string.Join(";", sel.Where(s => s != "").Distinct()) };
                        }
                    }
                    FlowService<MeasurePutLinePreCheckFlow> flow = new FlowService<MeasurePutLinePreCheckFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    MeasurePutLinePreCheckProject project = MeasurePutLinePreCheckProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "提交成功" };
                }
                if (record.BusinessClass == "RealEstatePreCheckSurveyAutoFlow") {
                    if (actionId == "0") {
                        PreSurvey_ContentInfo pc = service.GetById<PreSurvey_ContentInfo>(id);
                        var rec = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(pc.ProjectPlanPermission);
                        var sel = rec.Select(r => CanProjectPlanPermissionUse(id, "RealEstatePreSurveyFlow", r.Code));
                        if (sel.Any(s => s != "")) {
                            return new ApiResult { StateCode = 0, Message = string.Join(";", sel.Where(s => s != "").Distinct()) };
                        }
                        var selCheck = rec.Select(r => CanProjectPlanPermissionUse(id, "RealEstatePreCheckSurveyAutoFlow", r.Code));
                        if (selCheck.Any(s => s != "")) {
                            return new ApiResult { StateCode = 0, Message = string.Join(";", selCheck.Where(s => s != "").Distinct()) };
                        }
                    }
                    FlowService<RealEstatePreCheckSurveyAutoFlow> flow = new FlowService<RealEstatePreCheckSurveyAutoFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    RealEstatePreCheckSurveyAutoProject project = RealEstatePreCheckSurveyAutoProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "提交成功" };
                }
                if (record.BusinessClass == "CouncilPlanCheckFlow") {
                    var content = service.GetById<CouncilPlanCheckContentInfo>(id);
                    var rec = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(content.ProjectPlanPermission);
                    var sel = rec.Select(r => CanProjectPlanPermissionUse(id, nameof(CouncilPlanCheckFlow), r.Code));
                    if (sel.Any(s => s != "")) {
                        return new ApiResult { StateCode = 0, Message = string.Join(";", sel.Where(s => s != "").Distinct()) };
                    }
                    FlowService<CouncilPlanCheckFlow> flow = new FlowService<CouncilPlanCheckFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    CouncilPlanCheckProject project = CouncilPlanCheckProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "提交成功" };
                }
                if (record.BusinessClass == "CouncilMeasurePutLinePreCheckFlow") {
                    if (actionId == "0") {
                        PutLine_ContentInfo pc = service.GetById<PutLine_ContentInfo>(id);
                        var rec = JsonConvert.DeserializeObject<List<ProjectPlanPermissionInfo>>(pc.ProjectPlanPermission);
                        var sel = rec.Select(r => CanProjectPlanPermissionUse(id, "CouncilMeasurePutLinePreCheckFlow", r.Code));
                        if (sel.Any(s => s != "")) {
                            return new ApiResult { StateCode = 0, Message = string.Join(";", sel.Where(s => s != "").Distinct()) };
                        }
                    }
                    FlowService<CouncilMeasurePutLinePreCheckFlow> flow = new FlowService<CouncilMeasurePutLinePreCheckFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    CouncilMeasurePutLinePreCheckProject project = CouncilMeasurePutLinePreCheckProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "提交成功" };
                }
                if (record.BusinessClass == "RealEstateOverallActualSurveyFlow") {
                    FlowService<RealEstateOverallActualSurveyFlow> flow = new FlowService<RealEstateOverallActualSurveyFlow>(service);
                    flow.FlowPostToNext(record.ID);
                    RealEstateOverallActualSurveyProject project = RealEstateOverallActualSurveyProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "提交成功" };
                } else {
                    return new ApiResult { StateCode = 0, Message = "待完善..." };
                }
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "提交失败，错误信息：" + e.Message + "" };
            }

        }

        /// <summary>
        /// 业务回退
        /// </summary>
        /// <param name="id"></param>
        /// <param name="actionId"></param>
        /// <param name="backReason"></param>
        /// <returns></returns>
        [HttpPost, Route("BackBusiness")]
        public async Task<ApiResult> BackBusiness(dynamic data) {
            string id = data?.id?.ToString();
            string actionId = data?.actionId?.ToString();
            string backReason = data?.backReason?.ToString();

            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "ID不能为空，请传入正确的参数" };
            }
            var record = service.GetById<BusinessBaseInfo>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "退回失败，无法找到业务信息" };
            }
            var userId = UserInfo.UserId;
            string cannotFlowPostMessage;
            if (!CanFlowPost(record, actionId, userId, out cannotFlowPostMessage)) {
                return new ApiResult { StateCode = 0, Message = $"操作失败，{cannotFlowPostMessage}" };
            }
            try {
                //service.UpdateOne(record);
                Request.WriteSDCLog("业务办理", $"业务退回 >> 业务ID：{record.ID} >> 业务类型：{record.BusinessType} ");
                if (record.BusinessClass == "BaseSurveyDataDownloadFlow") {
                    FlowService<BaseSurveyDataDownloadFlow> flow = new FlowService<BaseSurveyDataDownloadFlow>(service);
                    flow.FlowPostToLast(record.ID, backReason);
                    return new ApiResult { StateCode = 1, Message = "退回成功" };
                }

                if (record.CreateUserId == userId) {
                    //如果是自己退给自己，当前环节没有签收，则赋值空ID
                    var action = service.GetById<BusinessLinkInfo>(actionId);
                    if (action.StateCode == 0 && action.CurrentUserID == null && action.SignatureTime == null) {
                        action.StateCode = 1;
                        action.CurrentUserID = Guid.Empty.ToString("N");
                        action.SignatureTime = DateTime.Now;
                        service.UpdateOne(action);
                    }
                }

                if (record.BusinessClass == "MeasurePutLineFlow") {
                    FlowService<MeasurePutLineFlow> flow = new FlowService<MeasurePutLineFlow>(service);
                    flow.FlowPostToLast(record.ID, backReason);
                    return new ApiResult { StateCode = 1, Message = "退回成功" };
                }
                else if (record.BusinessClass == "RealEstatePreSurveyFlow") {
                    FlowService<RealEstatePreSurveyFlow> flow = new FlowService<RealEstatePreSurveyFlow>(service);
                    flow.FlowPostToLast(record.ID, backReason);
                    return new ApiResult { StateCode = 1, Message = "退回成功" };
                }
                else if (record.BusinessClass == "RealEstatePreCheckSurveyFlow") {
                    FlowService<RealEstatePreCheckSurveyFlow> flow = new FlowService<RealEstatePreCheckSurveyFlow>(service);
                    flow.FlowPostToLast(record.ID, backReason);
                    return new ApiResult { StateCode = 1, Message = "退回成功" };
                }
                else if (record.BusinessClass == "BlueLineSurveyFlow") {
                    FlowService<BlueLineSurveyFlow> flow = new FlowService<BlueLineSurveyFlow>(service);
                    flow.FlowPostToLast(record.ID, backReason);
                    return new ApiResult { StateCode = 1, Message = "退回成功" };
                }
                else if (record.BusinessClass == "MarkPointSurveyFlow") {
                    FlowService<MarkPointSurveyFlow> flow = new FlowService<MarkPointSurveyFlow>(service);
                    flow.FlowPostToLast(record.ID, backReason);
                    return new ApiResult { StateCode = 1, Message = "退回成功" };
                }
                else if (record.BusinessClass == "PlanCheckSurveyFlow") {
                    //FlowService<PlanCheckSurveyFlow> flow = new FlowService<PlanCheckSurveyFlow>(service);
                    //flow.FlowPostToLast(record.ID, backReason);
                    //return new ApiResult { StateCode = 1, Message = "退回成功" };
                    return new ApiResult { StateCode = 0, Message = "退回失败，暂不支持该业务" };
                }
                else if (record.BusinessClass == "RealEstateActualSurveyFlow") {
                    //判断是否退回MDB
                    var isUploadMDBOnly = await XCloudService.CheckIsReplaceMDB(id, record.BusinessClass);

                    FlowService<RealEstateActualSurveyFlow> flow = new FlowService<RealEstateActualSurveyFlow>(service);
                    flow.FlowPostToLast(record.ID, backReason, isUploadMDBOnly);
                    return new ApiResult { StateCode = 1, Message = "退回成功" };
                } else if (record.BusinessClass == "RealEstatePreSurveyBuildingTableChangeFlow") {
                    FlowService<RealEstatePreSurveyBuildingTableChangeFlow> flow = new FlowService<RealEstatePreSurveyBuildingTableChangeFlow>(service);
                    flow.FlowPostToLast(record.ID, backReason);
                    return new ApiResult { StateCode = 1, Message = "退回成功" };
                } else if (record.BusinessClass == "RealEstatePreSurveyResultChangeFlow") {
                    FlowService<RealEstatePreSurveyResultChangeFlow> flow = new FlowService<RealEstatePreSurveyResultChangeFlow>(service);
                    flow.FlowPostToLast(record.ID, backReason);
                    return new ApiResult { StateCode = 1, Message = "退回成功" };
                } else if (record.BusinessClass == "RealEstateActualBuildingTableChangeFlow") {
                    FlowService<RealEstateActualBuildingTableChangeFlow> flow = new FlowService<RealEstateActualBuildingTableChangeFlow>(service);
                    flow.FlowPostToLast(record.ID, backReason);
                    return new ApiResult { StateCode = 1, Message = "退回成功" };
                } else if (record.BusinessClass == "RealEstateActualResultChangeFlow") {
                    FlowService<RealEstateActualResultChangeFlow> flow = new FlowService<RealEstateActualResultChangeFlow>(service);
                    flow.FlowPostToLast(record.ID, backReason);
                    return new ApiResult { StateCode = 1, Message = "退回成功" };
                } else if (record.BusinessClass == "MeasurePutLinePreCheckFlow") {
                    FlowService<MeasurePutLinePreCheckFlow> flow = new FlowService<MeasurePutLinePreCheckFlow>(service);
                    flow.FlowPostToLast(record.ID, backReason);
                    return new ApiResult { StateCode = 1, Message = "退回成功" };
                } else if (record.BusinessClass == "RealEstatePreCheckSurveyAutoFlow") {
                    FlowService<RealEstatePreCheckSurveyAutoFlow> flow = new FlowService<RealEstatePreCheckSurveyAutoFlow>(service);
                    flow.FlowPostToLast(record.ID, backReason);
                    return new ApiResult { StateCode = 1, Message = "退回成功" };
                } else if (record.BusinessClass == "CouncilPlanCheckFlow") {
                    //判断是否退回MDB
                    var isUploadMDBOnly = await XCloudService.CheckIsReplaceMDB(id, record.BusinessClass);

                    FlowService<CouncilPlanCheckFlow> flow = new FlowService<CouncilPlanCheckFlow>(service);
                    flow.FlowPostToLast(record.ID, backReason, isUploadMDBOnly);
                    return new ApiResult { StateCode = 1, Message = "退回成功" };
                } else if (record.BusinessClass == "CouncilMeasurePutLinePreCheckFlow") {
                    FlowService<CouncilMeasurePutLinePreCheckFlow> flow = new FlowService<CouncilMeasurePutLinePreCheckFlow>(service);
                    flow.FlowPostToLast(record.ID, backReason);
                    return new ApiResult { StateCode = 1, Message = "退回成功" };
                } else if (record.BusinessClass == "RealEstateOverallActualSurveyFlow") {
                    //判断是否退回MDB
                    var isUploadMDBOnly = await XCloudService.CheckIsReplaceMDB(id, record.BusinessClass);

                    FlowService<RealEstateOverallActualSurveyFlow> flow = new FlowService<RealEstateOverallActualSurveyFlow>(service);
                    flow.FlowPostToLast(record.ID, backReason, isUploadMDBOnly);
                    return new ApiResult { StateCode = 1, Message = "退回成功" };
                } else {
                    return new ApiResult { StateCode = 0, Message = "退回失败，没有找到适合的业务" };
                }
            }
            catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "退回失败，错误信息：" + e.Message + "" };
            }
        }

        /// <summary>
        /// 根据业务ID获取业务信息
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet, Route("GetBusinessByID")]
        public async Task<ApiResult> GetBusinessByID(string id) {
            if (string.IsNullOrWhiteSpace(id)) {
                return new ApiResult { StateCode = 0, Message = "请输入正确的信息" };
            }
            var record = service.GetById<BusinessBaseInfo>(id);
            if (record == null) {
                return new ApiResult { StateCode = 0, Message = "无法找到业务信息" };
            }
            try {
                if (record.BusinessClass == "BaseSurveyDataDownloadFlow") {
                    BaseSurveyDataDownloadProject project = BaseSurveyDataDownloadProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "" };
                }
                if (record.BusinessClass == "MeasurePutLineFlow") {
                    MeasurePutLineProject project = MeasurePutLineProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "" };
                }
                if (record.BusinessClass == "RealEstatePreSurveyFlow") {
                    RealEstatePreSurveyProject project = RealEstatePreSurveyProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "" };
                }
                if (record.BusinessClass == "RealEstatePreCheckSurveyFlow") {
                    RealEstatePreCheckSurveyProject project = RealEstatePreCheckSurveyProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "" };
                }
                if (record.BusinessClass == "BlueLineSurveyFlow") {
                    BlueLineSurveyProject project = BlueLineSurveyProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "" };
                }
                if (record.BusinessClass == "MarkPointSurveyFlow") {
                    MarkPointSurveyProject project = MarkPointSurveyProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "" };
                }
                if (record.BusinessClass == "PlanCheckSurveyFlow") {
                    //PlanCheckSurveyProject project = PlanCheckSurveyProject.GetByBusinessID(id);
                    //return new ApiResult { StateCode = 1, Data = project, Message = "" };
                    return new ApiResult { StateCode = 0, Message = "获取失败，错误信息：暂不支持该业务" };

                }
                if (record.BusinessClass == "RealEstateActualSurveyFlow") {
                    RealEstateActualSurveyProject project = await RealEstateActualSurveyProject.GetByBusinessID(id, true);
                    if (project.BaseInfo.StateCode == 2) {
                            var hasRecord = project.Attachments?.Any(a => a.AttachmentCategories == "不动产实核测绘成果审核反馈") ?? false;
                        if (!hasRecord) {
                            project.Attachments = project.Attachments.Where(a => a.AttachmentCategories != "竣工规划条件核实信息表").ToList();
                        }
                    }
                    return new ApiResult { StateCode = 1, Data = project, Message = "" };
                }
                if (record.BusinessClass == "RealEstatePreSurveyBuildingTableChangeFlow") {
                    RealEstatePreSurveyBuildingTableChangeProject project = await RealEstatePreSurveyBuildingTableChangeProject.GetByBusinessID(id, true);
                    return new ApiResult { StateCode = 1, Data = project, Message = "" };
                }
                if (record.BusinessClass == "RealEstatePreSurveyResultChangeFlow") {
                    RealEstatePreSurveyResultChangeProject project = await RealEstatePreSurveyResultChangeProject.GetByBusinessID(id, true);
                    return new ApiResult { StateCode = 1, Data = project, Message = "" };
                }
                if (record.BusinessClass == "RealEstateActualBuildingTableChangeFlow") {
                    RealEstateActualBuildingTableChangeProject project = RealEstateActualBuildingTableChangeProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "" };
                }
                if (record.BusinessClass == "RealEstateActualResultChangeFlow") {
                    RealEstateActualResultChangeProject project = await RealEstateActualResultChangeProject.GetByBusinessID(id, true);
                    if (project.BaseInfo.StateCode == 2) {
                            var hasRecord = project.Attachments?.Any(a => a.AttachmentCategories == "不动产实核测绘成果审核反馈") ?? false;
                        if (!hasRecord) {
                            project.Attachments = project.Attachments.Where(a => a.AttachmentCategories != "竣工规划条件核实信息表").ToList();
                        }
                    }
                    return new ApiResult { StateCode = 1, Data = project, Message = "" };
                }
                if (record.BusinessClass == "MeasurePutLinePreCheckFlow") {
                    MeasurePutLinePreCheckProject project = await MeasurePutLinePreCheckProject.GetByBusinessID(id, true);
                    return new ApiResult { StateCode = 1, Data = project, Message = "" };
                }
                if (record.BusinessClass == "RealEstatePreCheckSurveyAutoFlow") {
                    RealEstatePreCheckSurveyAutoProject project = RealEstatePreCheckSurveyAutoProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "" };
                }
                if (record.BusinessClass == "CouncilPlanCheckFlow") {
                    CouncilPlanCheckProject project = await CouncilPlanCheckProject.GetByBusinessID(id, true);
                    return new ApiResult { StateCode = 1, Data = project, Message = "" };
                }
                if (record.BusinessClass == "CouncilMeasurePutLinePreCheckFlow") {
                    CouncilMeasurePutLinePreCheckProject project = CouncilMeasurePutLinePreCheckProject.GetByBusinessID(id);
                    return new ApiResult { StateCode = 1, Data = project, Message = "" };
                }
                if (record.BusinessClass == "RealEstateOverallActualSurveyFlow") {
                    RealEstateOverallActualSurveyProject project = await RealEstateOverallActualSurveyProject.GetByBusinessID(id, true);
                    if (project.BaseInfo.StateCode == 2) {
                            var hasRecord = project.Attachments?.Any(a => a.AttachmentCategories == "不动产实核测绘成果审核反馈") ?? false;
                        if (!hasRecord) {
                            project.Attachments = project.Attachments.Where(a => a.AttachmentCategories != "竣工规划条件核实信息表").ToList();
                        }
                    }
                    return new ApiResult { StateCode = 1, Data = project, Message = "" };
                }
                return new ApiResult { StateCode = 0, Data = "", Message = "...待完善" };
            }
            catch (Exception e) {
                Request.WriteSDCLog("获取业务详情", $"业务ID：{id} >> 错误信息：{e.GetStackTraces()} ");
                return new ApiResult { StateCode = 0, Message = "获取失败，错误信息：" + e.Message + "" };
            }
        }

        /// <summary>
        /// 查询我的业务列表
        /// </summary>
        /// <param name="find">筛选字符</param>
        /// <param name="type">0位当前业务，1为已办业务</param>
        /// <param name="stateCode">细化状态值，-1为当前类型的全部，>=0时为筛选值</param>
        /// <param name="pageIndex">当前页索引</param>
        /// <param name="pageSize">页码大小</param>
        /// <returns></returns>
        [HttpGet, Route("GetMyBusinessList")]
        public ApiResult GetMyBusinessList(string q = "", int stateCode = -1, int type = 0, int pageIndex = 1, int pageSize = 20) {
            //获取用户信息
            var userId = UserInfo.UserId;
            var personNo = UserInfo.PersonNo;

            //获取用户的基本角色
            UserRole roleName;
            CompanyBaseInfo company;
            //根据身份证号获取绑定测绘单位的信息以及员工角色
            GetSurveyCompanyInfoAndRoleByPersonNo(personNo, out roleName, out company);

            //构造查询逻辑
            var mainSql = MyProjectListItem.MainSql;
            var findCondition = string.IsNullOrWhiteSpace(q) ? null : "AND (BASE.BUSINESSNUMBER = :q OR BASE.BUSINESSNAME LIKE :likeq OR BASE.DEVELOPERNAME=:q OR BASE.BUSINESSTYPE=:q OR BASE.SURVEYCOMPANYNAME=:q)";
            var stateCodeCondition = stateCode < 0 ? null : $"AND BASE.STATECODE={stateCode}";
            if (type == 1) {
                //查询已办业务
                if (company != null && company.CompanyType.CompanyBaseTypeFromName() == CompanyBaseType.SurveyCompany) {
                    //测绘单位，已接收到或者委托过的非在办业务
                    mainSql +=
                        $"WHERE (BASE.SURVEYCOMPANYNO='{company.CreditCode}' OR BASE.BUSINESSCLASS='BaseSurveyDataDownloadFlow' AND CREATEUSERID='{userId}') AND (ACT.ID IS NULL OR ACT.ACTIONUSERROLE!='SurveyMaster')";
                }
                else {
                    //个人和建设单位
                    mainSql +=
                        $"WHERE (EXISTS (SELECT 1 FROM BUSINESSLINKINFO WHERE CURRENTUSERID='{userId}' AND STATECODE IN(2,4) AND BUSINESSID=BASE.ID)\n" +
                        $"AND NOT EXISTS (SELECT 1 FROM BUSINESSLINKINFO WHERE CURRENTUSERID='{userId}' AND STATECODE IN(0,1,3) AND BUSINESSID=BASE.ID)\n" +
                        $"OR BASE.BUSINESSCLASS='BaseSurveyDataDownloadFlow' AND CREATEUSERID='{userId}')";
                }

                if (!string.IsNullOrWhiteSpace(findCondition)) {
                    mainSql += "\n" + findCondition;
                }
                if (!string.IsNullOrWhiteSpace(stateCodeCondition)) {
                    mainSql += "\n" + stateCodeCondition;
                }
                string order = "ACT.STARTTIME";
                switch (stateCode) {
                    case 0:
                        order = "BASE.CREATETIME";//待签收  按创建时间
                        break;
                    case 1:
                        order = "ACT.SIGNATURETIME";//办理中  按签收时间
                        break;
                    case 2:
                        order = "BASE.FINISHTIME";//已完成  按完成时间
                        break;
                    case 3:
                        order = "BASE.CREATETIME";//已退回  按创建时间
                        break;
                    case 4:
                        order = "ACT.ENDTIME";//已关闭  按关闭时间
                        break;
                }

                var result = !string.IsNullOrWhiteSpace(findCondition)
                    ? service.GetPageResultWithSql<MyProjectListItem>(mainSql, order, pageIndex, pageSize
                        , new OracleParameter(":q", OracleDbType.Varchar2) { Value = q }
                        , new OracleParameter(":likeq", OracleDbType.Varchar2) { Value = $"%{q}%" }
                        )
                    : service.GetPageResultWithSql<MyProjectListItem>(mainSql, order, pageIndex, pageSize);
                return new ApiResult() { StateCode = 1, Data = result };
            }
            else {
                //查询在办业务


                if (roleName == UserRole.Myself) {
                    //如果是个人，则只需要查询UserId与自己相关的
                    mainSql += $"WHERE (ACT.CURRENTUSERID = '{userId}' AND ACT.STATECODE IN(0,1,3)) ";
                }
                else if (company != null) {
                    //如果是建设单位，则按照正常的名下业务查看
                    if (company.CompanyType.CompanyBaseTypeFromName() == CompanyBaseType.ConstructionCompany) {
                        mainSql += $"WHERE  ACT.STATECODE IN(0,1,3) AND ((ACT.CURRENTUSERID = '{userId}')\n" +
                                   $"OR (ACT.CURRENTUSERID IS NULL AND TS.ROLECODE={(int)roleName} AND BASE.DEVELOPERNO='{company.CreditCode}'))";
                    }
                    //如果是测绘单位，则查看所有的
                    else if (company.CompanyType.CompanyBaseTypeFromName() == CompanyBaseType.SurveyCompany) {
                        mainSql +=
                            $"WHERE ACT.STATECODE IN(0,1,3) AND ACT.ACTIONUSERROLE='SurveyMaster' AND BASE.SURVEYCOMPANYNO='{company.CreditCode}'";
                    }
                    else {
                        mainSql += "WHERE 1=0";
                    }

                }
                else {
                    mainSql += "WHERE 1=0";
                }
                //增加筛选条件
                if (!string.IsNullOrWhiteSpace(findCondition)) {
                    mainSql += "\n" + findCondition;
                }
                if (!string.IsNullOrWhiteSpace(stateCodeCondition)) {
                    mainSql += "\n" + stateCodeCondition;
                }

                var result = !string.IsNullOrWhiteSpace(findCondition)
                    ? service.GetPageResultWithSql<MyProjectListItem>(mainSql, "STARTTIME", pageIndex, pageSize
                        , new OracleParameter(":q", OracleDbType.Varchar2) { Value = q }
                        , new OracleParameter(":likeq", OracleDbType.Varchar2) { Value = $"%{q}%" }
                        )
                    : service.GetPageResultWithSql<MyProjectListItem>(mainSql, "STARTTIME", pageIndex, pageSize);
                return new ApiResult() { StateCode = 1, Data = result };
            }
        }

        /// <summary>
        /// 根据业务逻辑信息判断用户是否能创建业务
        /// </summary>
        /// <param name="businessClass"></param>
        /// <returns></returns>
        [HttpGet, Route("IsFlowCanCreate")]
        public ApiResult IsFlowCanCreate(string businessClass) {
            if (string.IsNullOrWhiteSpace(businessClass)) {
                return new ApiResult { StateCode = 0, Message = "参数businessClass不能为空" };
            }
            //获取流程信息
            var type = Type.GetType($"SDCPCWeb.Models.BusinessFlow.{businessClass}");
            if (type == null) {
                return new ApiResult { StateCode = 0, Message = $"业务类型代码{businessClass}不支持" };
            }

            //获取第一个环节的执行者角色

            //如果是规划验线流程
            if (businessClass == nameof(MeasurePutLinePreCheckFlow) || businessClass == nameof(CouncilMeasurePutLinePreCheckFlow)) {
                //判断单位
                var companyid = Request.GetSDCCompanyIdFromCookie();
                if (!string.IsNullOrWhiteSpace(companyid)) {
                    var company = service.GetById<CompanyBaseInfo>(companyid);
                    if (company != null) {
                        var ctype = company.CompanyType.CompanyTypeFromName();
                        if (ctype != CompanyType.NormalCompany && ctype != CompanyType.Institute) {
                            return new ApiResult {StateCode = 1, Data = false, Message = "非房开单位才可以单独申请办理规划验线业务"};
                        }
                    }
                }
            }

            var flow = BusinessFlowConfig.GetFlow(type);
            if (flow == null) {
                return new ApiResult { StateCode = 0, Message = $"业务类型代码{businessClass}不支持" };
            }
            if (flow.Enable == false) {
                return new ApiResult { StateCode = 0, Message = $"业务类型代码{businessClass}不可用" };
            }
            var firstActionRoles = flow.FlowActionInfo.Actions
                .FirstOrDefault(a => a.ID == flow.FlowActionInfo.StartActionId)?.ActionRoles;
            if (firstActionRoles == null || !firstActionRoles.Any()) {
                return new ApiResult { StateCode = 0, Message = $"业务类型代码{businessClass}的设置不正确" };
            }

            //获取用户信息
            var userid = UserInfo.UserId;
            var personNo = UserInfo.PersonNo;

            if (string.IsNullOrWhiteSpace(userid) || string.IsNullOrWhiteSpace(personNo)) {
                return new ApiResult { StateCode = 0, Message = "用户信息获取失败，无法判断" };
            }

            //根据角色和场景判断是否匹配
            foreach (var role in firstActionRoles) {
                if (BusinessFlowConfig.CheckRole(userid, personNo, role, null)) {
                    return new ApiResult { StateCode = 1, Data = true, Message = "用户可创建此类型业务" };
                }
            }

            return new ApiResult { StateCode = 1, Data = false, Message = "您暂无权限创建此类型的业务" };
        }

        /// <summary>
        /// 获取业务类型列表
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("GetFlowList")]
        public ApiResult GetFlowList() {
            return new ApiResult { StateCode = 1, Data = BusinessFlowConfig.GetFlowList() };
        }

        /// <summary>
        /// 分页获取企业用户业务列表
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost, Route("GetUserBusinessList")]
        public async Task<ApiResult> GetUserBusinessList(dynamic data) {
            string companyNo = data?.companyNo?.ToString();
            string phone = data?.phone?.ToString();
            string userType = data?.userType?.ToString(); //用户类型
            int pageIndex = Convert.ToInt32(string.IsNullOrWhiteSpace(data?.pageIndex?.ToString()) ? "1" : data?.pageIndex?.ToString());
            int pageSize = Convert.ToInt32(string.IsNullOrWhiteSpace(data?.pageSize?.ToString()) ? "20" : data?.pageSize?.ToString());
            if (string.IsNullOrWhiteSpace(companyNo)) {
                return new ApiResult() {StateCode = 0, Message = "companyNo不能为空" };
            }

            if (string.IsNullOrWhiteSpace(phone)) {
                return new ApiResult() { StateCode = 0, Message = "phone不能为空" };
            }

            //获取当前登录用户信息
            var userId = UserInfo.UserId;

            var apiResult = GetCompanyInfoByUserInfo();
            if (apiResult.StateCode != 1) {
                return apiResult;
            }

            var jObj = JObject.FromObject(apiResult.Data);
            var companyList = JArray.FromObject(jObj["CompanyList"]);
            if (companyList.Any(s => s["CNo"].ToString() == companyNo && s["CRole"].ToString() == "单位管理员") == false) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = $"你不属于当前选择企业的管理员，不能操作"
                };
            }

            //如果是企业类型用户，就获取企业信息
            if (userType == "1") {
                string companySql = $"CreditCode=:companyNo AND ContacterPhone=:phone";
                var companyInfo = service.GetList<CompanyBaseInfo>(companySql,
                    new OracleParameter(":companyNo", OracleDbType.Varchar2) { Value = companyNo },
                    new OracleParameter(":phone", OracleDbType.Varchar2) { Value = phone });

                if (companyInfo.Any() == false) {
                    return new ApiResult() {
                        StateCode = 0,
                        Message = $"未查询到当前选择的用户所属单位信息，不能操作"
                    };
                }
                else {
                    phone = $"qy_{companyInfo.FirstOrDefault().CreditCode}";
                }
            }
            


            //查询选择的用户的信息
            string enPhone = AESEncrypt.Encrypt(phone);
            var handler = new HttpClientHandler() { UseCookies = false };
            var httpClient = HttpClientFactory.Create(handler);
            var url = $"{ExternalApiConfig.BDCWebServiceUrl}/suapi/GetUserInfoByUserName?enPhone={HttpUtility.UrlEncode(enPhone)}";
            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, url);
            httpRequestMessage.Headers.Add("Cookie", Request.Headers.GetCookies().FirstOrDefault()?.ToString());
            httpRequestMessage.Headers.Add("AuthCode", "SDCCommonAuthorize");
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;
            var httpResponseMessage = await httpClient.SendAsync(httpRequestMessage);
            if (httpResponseMessage.IsSuccessStatusCode == false) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = $"获取用户信息接口返回失败，状态码：{(int)httpResponseMessage.StatusCode}"
                };
            }
            var response = await httpResponseMessage.Content.ReadAsStringAsync();
            var result = JsonConvert.DeserializeObject<ApiResult>(response);
            if (result.StateCode != 1) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = $"获取用户信息接口返回失败：{result.Message}"
                };
            }

            var selectUserInfo = JObject.FromObject(result.Data);
            string selectUserId = selectUserInfo["Id"].ToString();

            string sql =
                $"select * from BusinessBaseInfo where CreateCompanyNo=:companyNo AND CreateUserId=:createUserId";

            var list = service.GetPageResultWithSql<BusinessBaseInfo>(sql, "CreateTime", pageIndex, pageSize,
                new OracleParameter(":companyNo", OracleDbType.Varchar2) { Value = companyNo },
                    new OracleParameter(":createUserId", OracleDbType.Varchar2) { Value = selectUserId });

            return new ApiResult() { StateCode = 1, Data = list };
        }

        /// <summary>
        /// 转移业务
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        [HttpPost, Route("TransferBusiness")]
        public async Task<ApiResult> TransferBusiness(dynamic data) {
            //需要转移和业务id
            string[] ids = JsonConvert.DeserializeObject<string[]>(JsonConvert.SerializeObject(data?.ids));

            //需要转移的用户的手机号
            string fromPhone = data?.fromPhone?.ToString();

            //需要转移到的用户的手机号
            string toPhone = data?.toPhone?.ToString();

            //需要转移到的用户的类型
            string toUserType = data?.toUserType?.ToString();

            //当前企业证件号
            string companyNo = data?.companyNo?.ToString();

            if (ids == null || ids.Any() == false) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "ids参数无效"
                };
            }

            if (string.IsNullOrWhiteSpace(fromPhone)) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "fromPhone参数无效"
                };
            }

            if (string.IsNullOrWhiteSpace(toPhone)) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "toPhone参数无效"
                };
            }

            if (string.IsNullOrWhiteSpace(companyNo)) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "compnayNo参数无效"
                };
            }

            //获取当前登录用户信息
            var userId = UserInfo.UserId;

            //获取当前企业信息
            var compnayBaseInfo = service.GetList<CompanyBaseInfo>($"CreditCode=:companyNo", new OracleParameter(":companyNo", OracleDbType.Varchar2) { Value = companyNo })
                .FirstOrDefault();
            if (compnayBaseInfo == null) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "当前企业不存在"
                };
            }

            //获取当前企业类型
            var companyType = compnayBaseInfo.CompanyType.CompanyTypeFromName();
            //判断用户是否属性该企业
            switch (companyType) {
                case CompanyType.SurveyCompany: {
                    var users = service.GetList<CompanyEmployees>($"PersonPhone=:phone",
                        new OracleParameter(":phone", OracleDbType.Varchar2) {Value = toPhone});
                    if (users.Select(s => s.RelationRequestId).Any(s => s == compnayBaseInfo.ID) == false) {
                        return new ApiResult() {
                            StateCode = 0,
                            Message = "当前企业不存在该用户"
                        };
                    }
                    break;
                }
                default: {
                    var users = service.GetList<DeveloperEmployee>($"PersonPhone=:phone",
                        new OracleParameter(":phone", OracleDbType.Varchar2) { Value = toPhone });
                    if (users.Select(s => s.RelationRequestId).Any(s => s == compnayBaseInfo.ID) == false) {
                        return new ApiResult() {
                            StateCode = 0,
                            Message = "当前企业不存在该用户"
                        };
                    }
                    break;
                }
            }

            //如果是企业类型用户，就获取企业信息
            if (toUserType == "1") {
                string companySql = $"CreditCode=:companyNo AND ContacterPhone=:phone";
                var companyInfo = service.GetList<CompanyBaseInfo>(companySql,
                    new OracleParameter(":companyNo", OracleDbType.Varchar2) { Value = companyNo },
                    new OracleParameter(":phone", OracleDbType.Varchar2) { Value = toPhone });

                if (companyInfo.Any() == false) {
                    return new ApiResult() {
                        StateCode = 0,
                        Message = $"未查询到当前选择的用户所属单位信息，不能操作"
                    };
                } else {
                    toPhone = $"qy_{companyInfo.FirstOrDefault().CreditCode}";
                }
            }


            //查询选择的用户的信息
            string enPhone = AESEncrypt.Encrypt(toPhone);
            var handler = new HttpClientHandler() { UseCookies = false };
            var httpClient = HttpClientFactory.Create(handler);
            var url = $"{ExternalApiConfig.BDCWebServiceUrl}/suapi/GetUserInfoByUserName?enPhone={HttpUtility.UrlEncode(enPhone)}";
            var httpRequestMessage = new HttpRequestMessage(HttpMethod.Post, url);
            httpRequestMessage.Headers.Add("Cookie", Request.Headers.GetCookies().FirstOrDefault()?.ToString());
            httpRequestMessage.Headers.Add("AuthCode", "SDCCommonAuthorize");
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls;
            var httpResponseMessage = await httpClient.SendAsync(httpRequestMessage);
            if (httpResponseMessage.IsSuccessStatusCode == false) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = $"获取用户信息接口返回失败，状态码：{(int)httpResponseMessage.StatusCode}"
                };
            }
            var response = await httpResponseMessage.Content.ReadAsStringAsync();
            var result = JsonConvert.DeserializeObject<ApiResult>(response);
            if (result.StateCode != 1) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = $"获取用户信息接口返回失败：{result.Message}"
                };
            }

            var toUserInfo = JObject.FromObject(result.Data);
            string toUserId = toUserInfo["Id"].ToString();
            string toPersonName = toUserInfo["PersonName"].ToString();
            string toPersonPhone = toUserType == "1" ? toUserInfo["PhoneNumber"].ToString() : toUserInfo["UserName"].ToString();
            string toPersonNo = toUserInfo["PersonNo"].ToString();


            //查询需要转移的业务
            List<string> transferIds = new List<string>();
            int page = 0;
            int pageSize = 999;
            var pageIds = ids.Skip(page * pageSize).Take(pageSize);
            string fromUserId = string.Empty;
            while (pageIds.Any()) {
                page++;

                var businessBaseInfos = service.GetList<BusinessBaseInfo>($"Id in ('{string.Join("','", pageIds)}') AND CreateCompanyNo=:companyNo AND CreatePersonPhone=:fromPhone", 
                    new OracleParameter(":companyNo", OracleDbType.Varchar2) { Value = companyNo },
                    new OracleParameter(":fromPhone", OracleDbType.Varchar2) { Value = fromPhone });

                if (businessBaseInfos.Any()) {
                    fromUserId = businessBaseInfos.FirstOrDefault().CreateUserId;

                    var businessBaseInfoIds = businessBaseInfos.Select(s => s.ID).ToArray();
                    transferIds.AddRange(businessBaseInfoIds);

                    //转移数据
                    //更新业务主表数据
                    string updateSql =
                        $"update BusinessBaseInfo set CreateUserId=:toUserId, CreatePersonName=:toPersonName, CreatePersonNo=:toPersonNo, CreatePersonPhone=:toPersonPhone where Id in ('{string.Join("','", businessBaseInfoIds)}')";
                    service.ExecuteUpdateSql(updateSql, 
                        new OracleParameter(":toUserId", OracleDbType.Varchar2) { Value = toUserId },
                        new OracleParameter(":toPersonName", OracleDbType.Varchar2) { Value = toPersonName },
                        new OracleParameter(":toPersonNo", OracleDbType.Varchar2) { Value = toPersonNo },
                        new OracleParameter(":toPersonPhone", OracleDbType.Varchar2) { Value = toPersonPhone }
                    );

                    //更新流转表数据
                    string updateSql1 = $"update BusinessLinkInfo set CurrentUserId=:toUserId where BusinessId in ('{string.Join("','", businessBaseInfoIds)}') and CurrentUserId=:fromUserId";
                    service.ExecuteUpdateSql(updateSql1,
                        new OracleParameter(":toUserId", OracleDbType.Varchar2) { Value = toUserId },
                        new OracleParameter(":fromUserId", OracleDbType.Varchar2) { Value = fromUserId }
                    );

                }

                pageIds = ids.Skip(page * pageSize).Take(pageSize);
            }

            //写日志
            if (transferIds.Any()) {
                List<string> sqlList = new List<string>();
                foreach (string id in transferIds) {
                    sqlList.Add($"insert into TransferBusinessLog(ID, CREATETIME, CREATEUSERID, FROMUSERID, TOUSERID, BusinessBaseInfoID) values('{Guid.NewGuid().ToString("N")}', SYSDATE, '{userId}', '{fromUserId}', '{toUserId}', '{id}')");
                }
                service.BatchExecuteUpdateSql(sqlList);
            }

            return new ApiResult() {
                StateCode = 1,
                Message = $"已转移{transferIds.Count}条业务"
            };
        }

        /// <summary>
        /// 获取土地用途列表
        /// </summary>
        /// <returns></returns>
        [HttpGet, Route("GetLandUseList")]
        public async Task<ApiResult> GetLandUseList() {
            var result = await XCloudService.GetLandUseListCache();
            if (!string.IsNullOrWhiteSpace(result.Item1)) {
                return new ApiResult() {
                    StateCode = 0,
                    Message = "获取土地用途失败：" + result.Item1
                };
            }

            return new ApiResult { StateCode = 1, Data = result.Item2 };
        }
        #endregion

        #region 功能性私有方法

        /// <summary>
        /// 根据用户身份证号获取所属测绘单位和角色名称
        /// </summary>
        /// <param name="personNo"></param>
        /// <param name="roleName"></param>
        /// <param name="company"></param>
        private void GetSurveyCompanyInfoAndRoleByPersonNo(string personNo, out UserRole roleName, out CompanyBaseInfo company) {
            //初始为个人，不属于任何单位
            roleName = UserRole.Myself;
            company = null;

            var memberList = service.GetList<CompanyEmployees>($"PersonNumber='{personNo}'");
            if (memberList.Any()) {
                //逻辑上不会存在一个人属于多个测绘单位的情况
                var personRole = memberList.First().PersonRole;
                company = service.GetList<CompanyBaseInfo>($"RelationRequestId='{memberList.First().RelationRequestId}'").FirstOrDefault();
                var companyType = company?.CompanyType.CompanyBaseTypeFromName();
                if (companyType == CompanyBaseType.SurveyCompany) {
                    roleName = personRole == "单位管理员" ? UserRole.SurveyAdmin :
                        personRole == "注册测绘师" ? UserRole.SurveyMaster : UserRole.SurveyNormal;
                }
            }
        }

        /// <summary>
        /// 调用Get请求获取数据
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        private string Get(string url) {
            using (var client = new WebClient() { Proxy = null, Encoding = Encoding.UTF8 }) {
                client.Headers.Add(HttpRequestHeader.ContentType, "application/json");
                client.Headers.Add(HttpRequestHeader.Accept, "text/html, application/xhtml+xml, */*");
                return client.DownloadString(url);
            }
        }
        /// <summary>
        /// get请求(xml结果)
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        public string HttpGet(string url) {
            Encoding encoding = Encoding.UTF8;
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
            request.Method = "GET";
            request.Accept = "text/html, application/xhtml+xml, */*";
            request.ContentType = "application/xml";
            HttpWebResponse response = (HttpWebResponse)request.GetResponse();
            using (StreamReader reader = new StreamReader(response.GetResponseStream(), Encoding.UTF8)) {
                return reader.ReadToEnd();
            }
        }

        /// <summary>
        /// 根据业务ID，环节ID和用户ID，判断用户是否能够处理当前环节的提交或者退回
        /// </summary>
        /// <param name="projectid"></param>
        /// <param name="actionid"></param>
        /// <param name="userid"></param>
        /// <param name="messageWhenFalse"></param>
        /// <returns></returns>
        private bool CanFlowPost(string projectid, string actionid, string userid, out string messageWhenFalse) {
            if (string.IsNullOrWhiteSpace(userid) || string.IsNullOrWhiteSpace(projectid) || string.IsNullOrWhiteSpace(actionid)) {
                messageWhenFalse = "参数错误";
                return false;
            }
            var baseInfo = service.GetById<BusinessBaseInfo>(projectid);
            var action = service.GetById<BusinessLinkInfo>(actionid);

            return CanFlowPost(baseInfo, action, userid, out messageWhenFalse);
        }
        private bool CanFlowPost(BusinessBaseInfoModel baseInfo, string actionid, string userid, out string messageWhenFalse) {
            if (string.IsNullOrWhiteSpace(userid) || string.IsNullOrWhiteSpace(actionid)) {
                messageWhenFalse = "参数错误";
                return false;
            }
            var action = service.GetById<BusinessLinkInfo>(actionid);

            return CanFlowPost(baseInfo, action, userid, out messageWhenFalse);
        }

        private bool CanFlowPost(BusinessBaseInfoModel baseInfo, BusinessLinkInfoModel action, string userid, out string messageWhenFalse) {
            messageWhenFalse = "参数错误";
            if (baseInfo == null || action == null) {
                return false;
            }

            //判断逻辑：
            //1、当前业务状态和环节状态必须一致，且必须等于1
            //2、当前环节的用户必须是当前操作者
            if (baseInfo.StateCode == 1 && action.StateCode == 1 && action.CurrentUserID == userid) {
                messageWhenFalse = null;
                return true;
            }

            if (action.ActionUserRole == UserRole.SurveyMaster.ToString()) {
                //注册测绘师刷脸确认环节
                messageWhenFalse = null;
                return true;
            }

            if (baseInfo.StateCode == 0 || baseInfo.StateCode == 3) {
                messageWhenFalse = "请先签收";
            }

            else if (baseInfo.StateCode == 2) {
                messageWhenFalse = "业务已办结";
            }

            else if (baseInfo.StateCode == 4) {
                messageWhenFalse = "业务已关闭";
            }

            else if (action.StateCode == 0 || baseInfo.StateCode == 3) {
                messageWhenFalse = "请先签收";
            }

            else if (action.StateCode == 2) {
                messageWhenFalse = "该环节已完成，请勿重复操作";
            }

            else if (action.CurrentUserID != userid) {
                messageWhenFalse = "您不具有此操作的权限";
            }

            return false;
        }

        /// <summary>
        /// 分段对参数进行UrlEncode
        /// </summary>
        /// <param name="toEncodeString"></param>
        /// <returns></returns>
        private string LongUrlEncode(string toEncodeString) {
            const int limit = 4000;
            var sb = new StringBuilder();
            var loops = toEncodeString.Length / limit;
            for (int i = 0; i <= loops; i++) {
                sb.Append(Uri.EscapeDataString(i < loops
                    ? toEncodeString.Substring(limit * i, limit)
                    : toEncodeString.Substring(limit * i)));
            }

            return sb.ToString();
        }
        /// <summary>
        /// 判断工规证号或者项目编号是否可用
        /// </summary>
        /// <param name="businessId"></param>
        /// <param name="businessClass"></param>
        /// <param name="code"></param>
        /// <returns></returns>
        private string CanProjectPlanPermissionUse(string businessId, string businessClass, string code) {
            string result = "";
            //放线测量
            if (businessClass == "MeasurePutLineFlow" || businessClass == "CouncilMeasurePutLinePreCheckFlow") {
                var estateProject = EstateProjectInfo.GetEstateProjectInfo(code); //service.GetById<EstateProjectInfo>(code);
                if (estateProject != null && (estateProject.PutLineSurveyState == SurveyState.CompleteSurvey || estateProject.PutLineSurveyState == SurveyState.CompleteCorrection)) {
                    if (estateProject.PutLineSurveyID != businessId) {
                        result = $"工程规划许可证号[{code}]已存在对应的放线测量业务，并且已完成测绘成果验收";
                        return result;
                    }
                }
                string sql = "select * FROM PUTLINE_CONTENTINFO p INNER JOIN BUSINESSBASEINFO b ON p.ID=b.ID WHERE b.STATECODE in ('0','1','2','3') AND  p.PROJECTPLANPERMISSION like :code and p.ID!=:id";
                int count = service.GetTotalCountWithSql(sql
                    , new OracleParameter(":code", OracleDbType.Varchar2) { Value = $"%{code}%" }
                    , new OracleParameter(":id", OracleDbType.Varchar2) { Value = businessId }
                    );
                if (count > 0) {
                    result = $"编号[{code}]存在正在委托的放线测量";
                    return result;
                }
            }
            //预测绘
            if (businessClass == "RealEstatePreSurveyFlow" 
                || businessClass == "RealEstatePreCheckSurveyFlow"
                || businessClass == "RealEstatePreCheckSurveyAutoFlow"
            ) {
                var estateProject = EstateProjectInfo.GetEstateProjectInfo(code); //service.GetById<EstateProjectInfo>(code);
                if (estateProject != null && (estateProject.PreSurveyState == SurveyState.CompleteSurvey || estateProject.PreSurveyState == SurveyState.CompleteAudit || estateProject.PreSurveyState == SurveyState.CompleteCorrection)) {
                    if (estateProject.PreSurveyID != businessId) {
                        result = $"工程规划许可证号[{code}]已存在对应的不动产预测绘业务，并且已完成测绘成果验收";
                        return result;
                    }
                }
                string sql = "select * FROM PRESURVEY_CONTENTINFO p INNER JOIN BUSINESSBASEINFO b ON p.ID=b.ID WHERE b.STATECODE in ('0','1','2','3') AND  p.PROJECTPLANPERMISSION like :code and p.ID!=:id";
                int count = service.GetTotalCountWithSql(sql
                    , new OracleParameter(":code", OracleDbType.Varchar2) { Value = $"%{code}%" }
                    , new OracleParameter(":id", OracleDbType.Varchar2) { Value = businessId }
                );
                if (count > 0) {
                    result = $"编号[{code}]存在正在委托的不动产预测绘";
                    return result;
                }

            }

            //市政工程建设竣工规划核实流程
            if (businessClass == nameof(CouncilPlanCheckFlow)) {
                string sql = "select * FROM CouncilPlanCheckContentInfo p INNER JOIN BUSINESSBASEINFO b ON p.ID=b.ID WHERE b.STATECODE in ('0','1','3') AND  p.PROJECTPLANPERMISSION like :code and p.ID!=:id";
                int count = service.GetTotalCountWithSql(sql
                    , new OracleParameter(":code", OracleDbType.Varchar2) { Value = $"%{code}%" }
                    , new OracleParameter(":id", OracleDbType.Varchar2) { Value = businessId }
                );
                if (count > 0) {
                    result = $"编号[{code}]存在正在委托的市政工程建设竣工规划核实流程";
                    return result;
                }
            }

            //预测绘判断工规证号是否已经被实核业务使用
            if (businessClass == "RealEstatePreSurveyFlow" 
                || businessClass == "RealEstatePreCheckSurveyFlow"
                || businessClass == "RealEstatePreCheckSurveyAutoFlow"
                || businessClass == "RealEstatePreSurveyResultChangeFlow"
               ) {

                //增加判断是否有已经办理过实核业务的工规证号 by liqisheng 2023-04-25
                var estateProjects = EstateProjectInfo.GetEstateProjectInfos(code);
                if (estateProjects.Any(s => s.RealSurveyID != null && (s.RealSurveyState == SurveyState.CompleteSurvey || s.RealSurveyState == SurveyState.CompleteAudit || s.RealSurveyState == SurveyState.CompleteCorrection))) {
                    result = $"工程规划许可证号[{code}]已办理不动产实核业务，不能再办理预测绘业务";
                    return result;
                }
            }

            return result;
        }

        /// <summary>
        /// 判断宗地号是否可用
        /// </summary>
        /// <param name="code">宗地号</param>
        /// <param name="name">业主单位名称</param>
        /// <returns></returns>
        private async Task<string> CanGroundCodeUse(string code, string name) {
            if (string.IsNullOrWhiteSpace(code))
                return "宗地号不能为空";

            if (code.Length != 7 && code.Length != 19)
                return $"宗地号[{code}]长度无效";

            //校验宗地号是否可用
            var servicesUrl = $"{ExternalApiConfig.EPSSDCServices}/IsZongDExits?strZDDM={code}";
            var httpClient = HttpClientFactory.Create();
            var responseMessage = await httpClient.GetAsync(servicesUrl);
            if (responseMessage.IsSuccessStatusCode) {
                var responseText = await responseMessage.Content.ReadAsStringAsync();
                System.Xml.XmlDocument xml = new System.Xml.XmlDocument();
                xml.LoadXml(responseText);
                JObject result = JObject.Parse(xml.InnerText);
                if (result["status"]?.ToString() != "success") {
                    return "校验宗地号返回失败，请联系管理员";
                }
                if (result["isZongDExits"]?.ToString() == "1") {
                    return "该宗地号不存在，请核实该宗地不动产单元号";
                } else if (result["isZongDExits"]?.ToString() == "2") {
                    return "该宗地不动产单元号未登记到您所在单位名下，请尽快办理该宗地不动产权证。（联系方式：南宁市不动产登记中心测绘管理部，电话0771-4306662）";
                } else if (result["isZongDExits"]?.ToString() == "3") {
                    //宗地号可用

                    //判断宗地号的权利人是否与当前业主单位名称一致
                    var qlrResult = await EPSService.GetZDQLR(code);
                    if (!string.IsNullOrWhiteSpace(qlrResult.Item1)) {
                        return qlrResult.Item1;
                    }
                    else {
                        if (qlrResult.Item2.Any(s => s["name"].ToString() == name) == false) {
                            return "该宗地不动产单元号所属权利人名称与当前业主单位名称不一致";
                        }
                    }
                    return "";
                } else {
                    return "校验宗地号返回的状态码未能识别，请联系管理员";
                }
            }
            else {
                return "校验宗地号接口异常，请联系管理员";
            }
        }

        /// <summary>
        /// 获取幢状态
        /// </summary>
        /// <param name="zrzguid">幢GUID，如有多个请使用英文逗号“,”分隔</param>
        /// <returns>Item1：返回消息，如果成功返回null；Item2：返回数据</returns>
        [NonAction]
        public static async Task<Tuple<string, JArray>> GetZRZZT(string zrzguid) {
            var uri = new Uri(ExternalApiConfig.BDCSystemApiUrl);
            uri = new Uri(uri, $"/api/bdcquery/GetZRZZT");
            var httpClient = HttpClientFactory.Create();
            using (StringContent content = new StringContent(JsonConvert.SerializeObject(new { @zrzguid = zrzguid }), Encoding.UTF8, "application/json")) {
                var responseMessage = await httpClient.PostAsync(uri.ToString(), content);
                if (responseMessage.StatusCode == HttpStatusCode.OK) {
                    var response = responseMessage.Content != null ? await responseMessage.Content.ReadAsStringAsync() : null;
                    var jObj = JsonConvert.DeserializeObject<JObject>(response);
                    if (jObj["IsSuccess"].ToObject<bool?>() == true) {
                        return new Tuple<string, JArray>(null, JArray.FromObject(jObj["Data"]));
                    }
                    else {
                        return new Tuple<string, JArray>(jObj["Message"]?.ToString(), null);
                    }
                }
                else {
                    return new Tuple<string, JArray>("获取幢状态失败，请联系管理员", null);
                }
            }
        }

        /// <summary>
        /// 根据状态码获取状态描述文字
        /// </summary>
        /// <param name="code"></param>
        /// <returns></returns>
        [NonAction]
        public static string GetZRZZTDescription(int status) {
            switch (status) {
                case 1:
                    return "该楼栋已办理建工程抵押，不可进行变更，详询南宁市不动产登记中心测绘管理部0771-4306662";
                case 2:
                    return "该楼栋已办理《预售证》，不可进行变更，详询南宁市不动产登记中心测绘管理部0771-4306662";
                case 3:
                    return "该楼栋已办理建工程抵押、《预售证》，不可进行变更，详询南宁市不动产登记中心测绘管理部0771-4306662";
                case 4:
                    return "该楼栋已办理建工程抵押、《预售证》、商品房网签备案，不可进行变更，详询南宁市不动产登记中心测绘管理部0771-4306662";
                case 5:
                    return "该楼栋有查封，不可进行变更，详询南宁市不动产登记中心测绘管理部0771-4306662";
                case 6:
                    return "该楼栋已办理建工程抵押、《预售证》、商品房网签备案、预告登记，不可进行变更，详询南宁市不动产登记中心测绘管理部0771-4306662";
                case 7:
                    return "该楼栋已办理建工程抵押、《预售证》、商品房网签备案、预告登记、预告登记，不可进行变更，详询南宁市不动产登记中心测绘管理部0771-4306662";
                default:
                    return "该楼栋有在办业务，不可变更，详询南宁市不动产登记中心测绘管理部0771-4306662";
            }
        }

        /// <summary>
        /// 根据状态码获取状态描述文字(云平台专用)
        /// </summary>
        /// <param name="status"></param>
        /// <returns></returns>
        [NonAction]
        public static string GetZRZZTDescriptionForXCloud(int status) {
            switch (status) {
                case 1:
                    return "该楼栋已办理建工程抵押";
                case 2:
                    return "该楼栋已办理《预售证》";
                case 3:
                    return "该楼栋已办理建工程抵押、《预售证》";
                case 4:
                    return "该楼栋已办理建工程抵押、《预售证》、商品房网签备案";
                case 5:
                    return "该楼栋有查封";
                case 6:
                    return "该楼栋已办理建工程抵押、《预售证》、商品房网签备案、预告登记";
                case 7:
                    return "该楼栋已办理建工程抵押、《预售证》、商品房网签备案、预告登记、预告登记";
                default:
                    return "该楼栋有在办业务";
            }
        }

        /// <summary>
        /// 校验幢GUID是否能使用
        /// </summary>
        /// <param name="businessId"></param>
        /// <param name="businessClass"></param>
        /// <param name="zrzguid">Item1:幢guid；Item2：坐落</param>
        /// <returns>返回失败的数据列表</returns>
        private List<string> CanZRZUse(string businessId, string businessClass, List<Tuple<string, string>> zrzguids) {
            List<string> fails = new List<string>();

            //楼盘表维护和成果变更业务
            if (businessClass == nameof(RealEstatePreSurveyBuildingTableChangeFlow)
                || businessClass == nameof(RealEstatePreSurveyResultChangeFlow)
                || businessClass == nameof(RealEstateActualBuildingTableChangeFlow)
                || businessClass == nameof(RealEstateActualResultChangeFlow)
            ) {
                foreach (var item in zrzguids) {
                    List<OracleParameter> parameters = new List<OracleParameter>();

                    string sql = "select * FROM BUSINESSBASEINFO WHERE STATECODE in ('0','1','3') and EXTENDINFO like :guid";
                    parameters.Add(new OracleParameter(":guid", OracleDbType.Varchar2) { Value = $"%{item.Item1}%" });

                    if (!string.IsNullOrWhiteSpace(businessId)) {
                        sql = "select * FROM BUSINESSBASEINFO WHERE STATECODE in ('0','1','3') and EXTENDINFO like :guid and ID!=:id";
                        parameters.Add(new OracleParameter(":id", OracleDbType.Varchar2) { Value = businessId });
                    }

                    int count = service.GetTotalCountWithSql(sql, parameters.ToArray());

                    if (count > 0) {
                        fails.Add($"[{item.Item2}]存在正在办理的业务");
                    }
                }
            }
            else {
                fails.Add($"楼幢检验不支持当前的[{businessClass}]业务类型");
            }

            return fails;
        }

        /// <summary>
        /// 获取用户所在单位
        /// </summary>
        /// <param name="personNo"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        [NonAction]
        private ApiResult GetCompanyInfoByUserInfo() {
            try {
                //判断是否是测绘单位
                var where = $"PersonNumber='{UserInfo.PersonNo}'";
                var suerveyCompanyEmployee = service.GetList<CompanyEmployees>(where).FirstOrDefault();
                if (suerveyCompanyEmployee == null) {
                    //判断是否已绑定为开发企业
                    var developerCompanyEmployees = service.GetList<DeveloperEmployee>(where);
                    if (!developerCompanyEmployees.Any()) {
                        //判断是否正在注册测绘单位
                        var registingCompany = service.GetList<CompanyRegisterRequest>($"CreateUserID='{UserInfo.UserId}' AND StateCode in(0,1,2,4,6)").FirstOrDefault();
                        if (registingCompany == null) {
                            //用户未绑定任何单位
                            return new ApiResult { StateCode = 0, Message = "未绑定单位信息或单位信息已失效" };
                        }
                        //用户正在注册测绘单位
                        dynamic cBriefInfo = new {
                            UserInfo.UserId,
                            registingCompany.CompanyType,
                            CompanyList = new[] {
                                    new {
                                        CID = registingCompany.ID,
                                        CName = registingCompany.CompanyName,
                                        CType = registingCompany.CompanyType,
                                        State = registingCompany.StateCode,
                                        CRole = "",
                                        CNo = registingCompany.CompanyNo
                                    }
                                }
                        };
                        return new ApiResult { StateCode = 1, Data = cBriefInfo };
                    } else {
                        var companyList = new List<object>();
                        //开发企业的人员
                        foreach (var developerEmployee in developerCompanyEmployees) {
                            var company = service.GetById<CompanyBaseInfo>(developerEmployee.RelationRequestId);
                            if (company != null) {
                                companyList.Add(new {
                                    CID = company.ID,
                                    CName = company.CompanyName,
                                    CType = company.CompanyType,
                                    State = 3,
                                    CRole = developerEmployee.PersonRole,
                                    CNo = company.CreditCode
                                });
                            }
                        }
                        dynamic cBriefInfo = new {
                            UserInfo.UserId,
                            CompanyType = CompanyBaseType.ConstructionCompany.ToName(),
                            CompanyList = companyList
                        };
                        return new ApiResult { StateCode = 1, Data = cBriefInfo };
                    }
                } else {
                    //绑定了已注册的测绘单位
                    var cRecord = service.GetById<CompanyBaseInfo>(suerveyCompanyEmployee.RelationRequestId);
                    if (cRecord != null) {
                        dynamic cBriefInfo = new {
                            UserInfo.UserId,
                            cRecord.CompanyType,
                            CompanyList = new[] {
                                new {
                                    CID = cRecord.ID,
                                    CName = cRecord.CompanyName,
                                    CType = cRecord.CompanyType,
                                    State = 3,
                                    CRole = suerveyCompanyEmployee.PersonRole,
                                    CNo = cRecord.CreditCode
                                }
                            }
                        };
                        return new ApiResult { StateCode = 1, Data = cBriefInfo };
                    }
                    return new ApiResult { StateCode = 0, Message = "未绑定单位信息或单位信息已失效" };
                }
            } catch (Exception e) {
                return new ApiResult { StateCode = 0, Message = "单位信息获取失败，错误信息：" + e.Message + "" };
            }
        }
        #endregion

    }

}